{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-1800, 600], "id": "84797f63-882e-4b29-b691-6abc9e747451", "name": "Limit"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE \n   vContador         NUMBER := 0; \n   vSaldo            NUMBER := 0; \n   vSaldoCredito     NUMBER := 0; \n   vSaldoDebito      NUMBER := 0; \n   vSaldoInicial     NUMBER := 0;\n   vCodBanco         NUMBER := {{ $('Saida').item.json.CODBANCO }};\n   vCodCob           VARCHAR2(1) := 'D';\n   vDtUltCompensacao DATE;\n   vAno              VARCHAR2(4);\n   vMes              VARCHAR2(2);\n   vCodFilial        NUMBER := {{ $('PROXNUMTRANS').item.json.CODFILIAL }};\n   vCodFunc          NUMBER := {{ $('Saida').item.json.CODFUNC }};\n   vDtInicial        DATE := TO_DATE('{{ $('Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD-MM-YYYY');\n   vDtFinal          DATE := TO_DATE('{{ $('Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD-MM-YYYY');\n   vFaixaNumTrans    VARCHAR2(20) := '{{ $('PROXNUMTRANS').item.json.PROXNUMTRANS }}';\nBEGIN \n   -- Extrai ano e mês da variável dhEmi\n   vAno := '{{ $('Saida').item.json.dhEmi.split('T')[0].split('-')[0]}}';\n   vMes := '{{ $('Saida').item.json.dhEmi.split('T')[0].split('-')[1]}}';\n   \n   SELECT MAX(TRUNC(DTULTCOMPENSACAO)) \n     INTO vDtUltCompensacao \n     FROM PCESTCR;\n   \n   BEGIN\n    SELECT NVL(SALDO, 0) \n      INTO vSaldoInicial \n      FROM PCESTCRCOMPENSACAO \n     WHERE CODBANCO = vCodBanco \n       AND CODMOEDA = vCodCob \n       AND ANO = vAno \n       AND MES = vMes \n    ORDER BY ANO, MES\n    FETCH FIRST 1 ROWS ONLY;\n  EXCEPTION\n    WHEN NO_DATA_FOUND THEN\n        vSaldoInicial := 0;\n   END;\n\n   \n   FOR REG IN (SELECT TIPO, \n                      VALOR, \n                      ROWID ID \n               FROM PCMOVCR \n               WHERE CODBANCO = vCodBanco\n                 AND CODCOB = vCodCob\n  ) \n   LOOP \n      IF LENGTH(REG.ID) > 0 THEN\n         UPDATE PCMOVCR\n            SET VLSALDOCOMP = vSaldoInicial + vSaldoDebito - vSaldoCredito\n          WHERE ROWID = REG.ID; \n      END IF; \n   END LOOP;\n   \n   SELECT SUM(DECODE(TIPO, 'D', NVL(VALOR, 0), 0)) TOTALDEBITOS,\n          SUM(DECODE(TIPO, 'C', NVL(VALOR, 0), 0)) TOTALCREDITOS\n     INTO vSaldoDebito,\n          vSaldoCredito\n     FROM PCMOVCR\n    WHERE CODBANCO = vCodBanco\n      AND CODCOB = vCodCob\n      AND ((OPERACAO <> 99) OR (OPERACAO IS NULL))\n      AND DTCOMPENSACAO IS NOT NULL; \n   \n   vSaldo := vSaldoDebito - vSaldoCredito; \n   vContador := 0;\n   \n   SELECT COUNT(1)\n     INTO vContador\n     FROM PCESTCR\n    WHERE CODCOB = vCodCob\n      AND CODBANCO = vCodBanco; \n   \n   IF vContador = 0 THEN \n      INSERT INTO PCESTCR \n         (CODCOB,   \n          CODBANCO, \n          VALOR,    \n          VALORSALDOTOTALCONCIL, \n          VALORSALDOTOTALCOMP, \n          VALORCONCILIADO, \n          VALORCOMPENSADO) \n      VALUES \n         (vCodCob, \n          vCodBanco,\n          0, \n          0, \n          0, \n          0, \n          0); \n   END IF; \n   \n   UPDATE PCESTCR \n      SET VALORSALDOTOTALCOMP   = vSaldo, \n          VALORCOMPENSADO       = vSaldo, \n          DTULTCOMPENSACAO      = vDtInicial,\n          VALORCONCILIADO       = vSaldo, \n          VALORSALDOTOTALCONCIL = vSaldo, \n          VALOR                 = vSaldo, \n          DTULTCONCILIA         = vDtInicial \n    WHERE CODBANCO = vCodBanco\n      AND CODCOB = vCodCob; \n   \n   -- Atualiza a tabela PCESTCRCOMPENSACAO ou insere novos dados se não existirem registros\n   SELECT COUNT(1)\n     INTO vContador\n     FROM PCESTCRCOMPENSACAO\n    WHERE CODBANCO = vCodBanco \n      AND CODMOEDA = vCodCob \n      AND MES = vMes           \n      AND ANO = vAno;\n   \n   IF vContador > 0 THEN\n      UPDATE PCESTCRCOMPENSACAO \n         SET VALORCREDITO     = vSaldoCredito, \n             VALORDEBITO      = vSaldoDebito,  \n             SALDOINICIAL     = vSaldoInicial, \n             SALDO            = vSaldo,         \n             DATASALDOINICIAL = vDtUltCompensacao\n       WHERE CODBANCO = vCodBanco \n         AND CODMOEDA = vCodCob \n         AND MES = vMes           \n         AND ANO = vAno;\n   ELSE\n      INSERT INTO PCESTCRCOMPENSACAO (MES, ANO, CODBANCO, CODMOEDA, VALORDEBITO, VALORCREDITO, DATASALDOINICIAL, SALDO, SALDOINICIAL)\n      VALUES (vMes, vAno, vCodBanco, vCodCob, vSaldoDebito, vSaldoCredito, vDtUltCompensacao, vSaldo, vSaldoInicial);\n   END IF;\n   \n   -- Insere registro na tabela PCCAIXABALCAO\n   INSERT INTO PCCAIXABALCAO(\n      NUMSEQ, \n      CODFILIAL, \n      DTFECHA, \n      DTINICIAL, \n      DTFINAL, \n      CODFUNC, \n      CODROTINA, \n      CODCOBBANCO, \n      FAIXANUMTRANS\n   ) \n   VALUES(\n      DFSEQ_PCCAIXABALCAO.NEXTVAL, \n      vCodFilial, \n      SYSDATE, \n      vDtInicial, \n      vDtFinal, \n      vCodFunc, \n      '410', \n      vCodBanco, \n      vFaixaNumTrans\n   ); \n   \n   -- Reset das variáveis\n   vSaldo := NULL; \n   vSaldoCredito := NULL; \n   vSaldoDebito := NULL; \n   vSaldoInicial := NULL;\n\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-820, 620], "id": "7b15d0ff-458d-454b-8601-90afa0aa08d1", "name": "<PERSON><PERSON><PERSON>", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "d5fc9e5a-9358-466b-8974-a1a076ebc40b", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "venda", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "bba4f9f2-97c6-450b-bd8d-e5ba2510a7c4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2000, 620]}, {"parameters": {"content": "# Acerto automático na 410 \n- visível na aba Acerto da 1240", "height": 120, "width": 494}, "id": "7940b7cf-f9e1-4110-97f9-956686ba0b74", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2960, 480]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}, {"id": "b163cce6-a7eb-4d4d-997f-943da7b84c51", "name": "CODUSURDEVOL", "value": 68, "type": "string"}, {"id": "baab24ae-9778-41f8-a3c3-7bcb002bc6cc", "name": "CODFUNC", "value": 1, "type": "number"}, {"id": "67c5933a-e817-4055-baa2-e866fd2189cd", "name": "CODBANCO", "value": 7777, "type": "number"}]}, "includeOtherFields": true, "options": {}}, "id": "e65a89bc-87b6-45c8-88b6-7ef4c95df6d9", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-2160, 620]}, {"parameters": {"jsCode": "// 4-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)\n    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) \n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det['prod']\n        const imposto = det['imposto']\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][\n          'verProc'\n        ].includes('mercadolivre')\n          ? 'CML'\n          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(\n              'Bling'\n            )\n          ? 'D2C'\n          : ''\n        // BC de IPI = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        // Obter valores das novas tags\n        const xPed = produto['xPed'] || '';\n        const infAdProd = det['infAdProd'] || '';\n        const infCpl = xmlData['nfeProc']['NFe']['infNFe']['infAdic']?.['infCpl'] || '';\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',\n          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',\n          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],\n          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          vFrete: vFrete,\n          vOutro: vOutro, \n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],\n          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,\n          vICMS:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],\n          vProd:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],\n          BASEICST: parseFloat(\n            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']\n          ),\n          VLBASEIPI: VLBASEIPI,\n\t\t\t\t\t/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, \n          vIPI:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||\n            0,\n          vIPIDevol:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][\n              'vIPIDevol'\n            ] || 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vPIS: pis ? pis['vPIS'] || 0 : 0,\n          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,\n          cPais: dest['enderDest']['cPais'],\n          xPais: dest['enderDest']['xPais'],\n          CEP: dest['enderDest']['CEP'],\n          ufDest: dest['enderDest']['UF'],\n          xMun: dest['enderDest']['xMun'],\n          xLgr: dest['enderDest']['xLgr'],\n          nro: dest['enderDest']['nro'],\n          xBairro: dest['enderDest']['xBairro'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CODCOB,\n          CST_PIS_COFINS, \n          xPed,\n          infAdProd,\n          infCpl,\n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "bc4d4968-91af-4abd-9e23-ebb243cdf2c8", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2320, 620]}, {"parameters": {"options": {}}, "id": "3f2ebebb-252d-453a-97d7-574e68fef7c1", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-2480, 620]}, {"parameters": {}, "id": "d4105e15-eac8-4ad2-822a-8f6946f366f4", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2960, 620]}, {"parameters": {"operation": "execute<PERSON>uery", "query": " SELECT 1 \n   FROM PCESTCRCOMPENSACAO   \n  WHERE CODBANCO = {{ $json.CODBANCO }}\n    AND CODMOEDA = 'D'\n    AND MES = '{{$('<PERSON><PERSON>').item.json.dhEmi.split('T')[0].split('-')[1]}}'\n    AND ANO = '{{$('<PERSON><PERSON>').item.json.dhEmi.split('T')[0].split('-')[0]}}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2120, 300], "id": "0217e713-2c98-40f8-a35a-3c406ac5e797", "name": "Validar Fechamento", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "disabled": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM \n     SET PROXNUMTRANS = NVL(PROXNUMTRANS,1) + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-620, 620], "id": "2f42b7ef-992e-4660-98bb-eefbcf313557", "name": "Atualizar a sequencia", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCR", "mode": "list", "cachedResultName": "PCMOVCR"}, "valuesToSend": {"values": [{"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANS').item.json.PROXNUMTRANS }}"}, {"name": "DATA", "value": "={{ $('<PERSON><PERSON>').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODBANCO", "value": "={{ $('<PERSON><PERSON>').item.json.CODBANCO }}"}, {"name": "CODCOB", "value": "D"}, {"name": "HISTORICO", "value": "=VLR REC. EM {{ $('<PERSON><PERSON>').item.json.CODCOB }} CARREG.0"}, {"name": "HISTORICO2", "value": "ACERTO CARGA. 0"}, {"name": "VALOR", "value": "={{ $('If').item.json.VALOR }}"}, {"name": "TIPO", "value": "D"}, {"name": "NUMCARR", "value": "0"}, {"name": "NUMDOC"}, {"name": "VLSALDO", "value": "={{ $('If').item.json.TOTALDEBITOS }}"}, {"name": "VLSALDOCONCIL", "value": "={{ $('If').item.json.TOTALDEBITOS }}"}, {"name": "DTCONCIL", "value": "={{ $('<PERSON><PERSON>').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFUNCCONCIL", "value": "={{ $('<PERSON><PERSON>').item.json.CODFUNC }}"}, {"name": "CONCILIACAO", "value": "OK"}, {"name": "VLSALDOCOMP"}, {"name": "COMPENSACAO", "value": "OK"}, {"name": "DTCOMPENSACAO", "value": "={{ $('<PERSON><PERSON>').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFUNCCOMP", "value": "={{ $('<PERSON><PERSON>').item.json.CODFUNC }}"}, {"name": "HORA", "value": "={{ $('Saida').item.json.dhEmi.split('T')[1].split(':')[0] }}"}, {"name": "MINUTO", "value": "={{ $('<PERSON>a').item.json.dhEmi.split('T')[1].split(':')[1] }}"}, {"name": "CODFUNC", "value": "={{ $('<PERSON><PERSON>').item.json.CODFUNC }}"}, {"name": "CODCONTADEB"}, {"name": "CODCONTACRED", "value": "100020"}, {"name": "INDICE", "value": "A"}, {"name": "CODROTINALANC", "value": "410"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1020, 620], "id": "7c06594f-921a-4610-97ed-dd8c16b1ca79", "name": "PCMOVCR", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE                                                                                             \n      vSaldoConta NUMBER := 0 ;                                                                         \n    BEGIN                                                                                               \n      vSaldoConta := :SaldoInicial;                                                                     \n                                                                                                        \n      FOR REG IN (SELECT PCMOVCR.ROWID                                                                  \n                        ,PCMOVCR.NUMTRANS                                                               \n                        ,PCMOVCR.DTCONCIL                                                               \n                        ,PCMOVCR.VALOR                                                                  \n                        ,PCMOVCR.TIPO                                                                   \n                        ,PCMOVCR.NUMCARR                                                                \n                        ,PCMOVCR.CODBANCO                                                               \n                        ,PCMOVCR.CODCOB                                                                 \n                        ,PCMOVCR.VLSALDOCONCIL                                                          \n                        ,PCMOVCR.DTCOMPENSACAO                                                          \n                    FROM PCMOVCR                                                                        \n                   WHERE PCMOVCR.CODBANCO    = :CODBANCO                                                \n                     AND PCMOVCR.CODCOB      = :CODMOEDA                                                \n                     AND PCMOVCR.CONCILIACAO = 'OK'                                                   \n                    AND  TRUNC(PCMOVCR.DTCONCIL) BETWEEN :DATAINICIAL AND :DATAFINAL                   \n                ORDER BY PCMOVCR.DTCONCIL, PCMOVCR.NUMSEQ, PCMOVCR.DATA, PCMOVCR.NUMTRANS )   LOOP      \n        IF (REG.TIPO = 'C') then                                                                      \n          vSaldoConta := vSaldoConta - REG.VALOR;                                                       \n        ELSE                                                                                            \n          vSaldoConta := vSaldoConta + REG.VALOR;                                                       \n        END IF;                                                                                         \n                                                                                                        \n        IF REG.ROWID > 0 THEN                                                                           \n           UPDATE PCMOVCR                                                                               \n              SET VLSALDOCONCIL  = vSaldoConta                                                          \n            WHERE NUMTRANS       = REG.NUMTRANS                                                         \n              AND ROWID          = REG.ROWID ;                                                          \n        END IF ;                                                                                        \n      END LOOP;                                                                                         \n                                                                                                        \n     SELECT SUM(DECODE(M.TIPO, 'D', VALOR, 0)) -                                                      \n            SUM(DECODE(M.TIPO, 'C', VALOR, 0))                                                        \n            INTO vSaldoConta                                                                            \n       FROM PCMOVCR M                                                                                   \n      WHERE M.CODCOB      = :CODMOEDA                                                                   \n        AND M.CODBANCO    = :CODBANCO                                                                   \n        AND M.CONCILIACAO = 'OK';                                                                     \n                                                                                                        \n      :SaldoConta := vSaldoConta ;                                                                      \n    END;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1880, 300], "id": "0676cff5-79c5-4eb5-a5c6-8e5e0fd560f3", "name": "Fechar Balcão1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "disabled": true}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH VL AS (SELECT SUM(VALOR) AS VALOR\n      FROM PCPREST\n      WHERE DTCANCEL IS NULL\n        AND NUMCAR = 0\n        AND NVL(CODFUNCCHECKOUT, 0) = 0\n        AND NVL(NUMCHECKOUT, 0) = 0\n        AND NVL(CODFUNCVEND, 0) = 0\n        AND CODFILIAL = '1'\n        AND DECODE(PERMITEESTORNO, 'S', DTEMISSAOORIG, DTEMISSAO) BETWEEN TO_DATE('{{ $('Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY') AND TO_DATE('{{ $('Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY')\n        AND CODCOB NOT IN ('DESD', 'CANC', 'ESTR', 'CRED')),\n  SALDO AS (SELECT SUM(DECODE(TIPO, 'D', NVL(VALOR, 0), 0)) TOTALDEBITOS,\n                   SUM(DECODE(TIPO, 'C', NVL(VALOR, 0), 0)) TOTALCREDITOS\n      FROM PCMOVCR\n      WHERE CODBANCO = {{ $json.CODBANCO }}\n        AND CODCOB = 'D'\n        AND ((OPERACAO <> 99)\n        OR (OPERACAO IS NULL))\n        AND DTCOMPENSACAO IS NOT NULL)\nSELECT *\n  FROM VL,\n       SALDO", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1620, 600], "id": "d573a369-9d65-4bec-89a3-bdd84c3efb5d", "name": "<PERSON><PERSON>", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT NVL(PR<PERSON><PERSON><PERSON><PERSON><PERSON>, 1) PROXNUMTRANS,\n(SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $('<PERSON><PERSON>').item.json.CGCFILIAL }}') AS CODFILIAL\n  FROM PCCONSUM FOR UPDATE", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1220, 620], "id": "497542ff-d300-45b9-bed1-56e337e7c885", "name": "PROXNUMTRANS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-2800, 620], "id": "ec22f113-0061-4a36-8937-e486396b04ce", "name": "Ler XML"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}, {"id": "8569c846-2c89-4eaf-b3bf-5f168f58b120", "leftValue": "={{ $json.VALOR }}", "rightValue": "", "operator": {"type": "number", "operation": "empty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1440, 600], "id": "e0965095-8450-47e7-be59-f397b96f4c60", "name": "If"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-2640, 620], "id": "3eab136a-546b-466e-955b-b9982fae2c57", "name": "Extract from File"}], "connections": {"Limit": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Fechar Balcão": {"main": [[{"node": "Atualizar a sequencia", "type": "main", "index": 0}]]}, "Saida": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "Validar Fechamento": {"main": [[]]}, "Atualizar a sequencia": {"main": [[]]}, "PCMOVCR": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Saldo Inicial": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "PROXNUMTRANS": {"main": [[{"node": "PCMOVCR", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "If": {"main": [[], [{"node": "PROXNUMTRANS", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}