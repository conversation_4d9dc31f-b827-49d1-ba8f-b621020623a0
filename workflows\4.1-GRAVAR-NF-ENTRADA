{"nodes": [{"parameters": {}, "id": "989037ce-aacc-4d2d-9407-448c3db63c96", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2780, 900]}, {"parameters": {"options": {}}, "id": "0916e57a-58b0-426a-8679-c6ee95ab098f", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-2300, 900]}, {"parameters": {"jsCode": "// 4-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)\n    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) \n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det['prod']\n        const imposto = det['imposto']\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][\n          'verProc'\n        ].includes('mercadolivre')\n          ? 'CML'\n          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(\n              'Bling'\n            )\n          ? 'D2C'\n          : ''\n        // BC de IPI = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        // Obter valores das novas tags\n        const xPed = produto['xPed'] || '';\n        const infAdProd = det['infAdProd'] || '';\n        const infCpl = xmlData['nfeProc']['NFe']['infNFe']['infAdic']?.['infCpl'] || '';\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',\n          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',\n          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],\n          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          vFrete: vFrete,\n          vOutro: vOutro, \n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],\n          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,\n          vICMS:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],\n          vProd:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],\n          BASEICST: parseFloat(\n            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']\n          ),\n          VLBASEIPI: VLBASEIPI,\n\t\t\t\t\t/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, \n          vIPI:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||\n            0,\n          vIPIDevol:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][\n              'vIPIDevol'\n            ] || 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vPIS: pis ? pis['vPIS'] || 0 : 0,\n          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,\n          cPais: dest['enderDest']['cPais'],\n          xPais: dest['enderDest']['xPais'],\n          CEP: dest['enderDest']['CEP'],\n          ufDest: dest['enderDest']['UF'],\n          xMun: dest['enderDest']['xMun'],\n          xLgr: dest['enderDest']['xLgr'],\n          nro: dest['enderDest']['nro'],\n          xBairro: dest['enderDest']['xBairro'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CODCOB,\n          CST_PIS_COFINS, \n          xPed,\n          infAdProd,\n          infCpl,\n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "f0cf8f47-46d7-46e8-84e9-ceac23b0e856", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2140, 900]}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "id": "c12c2948-aa45-4203-bb56-a451a5caf1ed", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-1980, 900]}, {"parameters": {"content": "## Regra Tributaria - finNFe - Finalidade de emissão da NF-e\n\n1 = NF-e normal.\n2 = NF-e complementar.\n3 = NF-e de ajuste.\n4 = Devolução de mercadoria.", "height": 209.7473416040146, "width": 388.85609947320665, "color": 6}, "id": "b8e382f9-955c-4850-990e-2f39268b2e1a", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2760, 660]}, {"parameters": {"content": "## PATH BASE TESTE\n/u01/xml_mimo/mercado_livre/TESTE/*.xml", "height": 199.87149532710242, "width": 350.64252336448556, "color": 4}, "id": "afe8b6e9-1ad4-42d0-9517-1e8589c5cdc4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2340, 660]}, {"parameters": {"content": "## Baixa automática de títulos com crédito de cliente\n**Na rotina 1286 é poss[ivel validar os dados** ", "height": 392, "width": 813, "color": 4}, "id": "1b2e008e-e2e5-460f-9132-a2fa900fb26d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, 540], "disabled": true}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}, {"id": "b163cce6-a7eb-4d4d-997f-943da7b84c51", "name": "CODUSURDEVOL", "value": 68, "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "bb530897-1e53-4204-b4a5-5c3fcaef9ed8", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-1820, 900]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT C.PROXNUMTRANSENT,\n       CLI.CODCLI,\n       C.CODCONTC<PERSON>, \n       C.CODCONTFOR,\n(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $('Loop').item.json.CGCFILIAL }}') AS CODFORNEC,\n(SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $('Loop').item.json.CGCFILIAL }}') AS CODFILIAL,\nDFSEQ_PCCRECLI_NUMCRED.NEXTVAL AS PROXNUMCRED\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE 1 = 1\n    AND APENASNUMEROS(CGCENT) = '{{ $('Loop').item.json.CGCENT }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-660, 760], "id": "310fd8ae-e665-4516-9775-5d59a173a370", "name": "PROXNUMTRANSENT", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFENT", "mode": "list", "cachedResultName": "PCNFENT"}, "valuesToSend": {"values": [{"name": "DTEMISSAO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "TIPOEMISSAO", "value": "={{ $('Loop').item.json.tpImp }}"}, {"name": "FINALIDADENFE", "value": "={{ $('Loop').item.json.finNFe }}"}, {"name": "SERIE", "value": "={{ $('Loop').item.json.serie }}"}, {"name": "NUMTRANSENT", "value": "={{ $json.PROXNUMTRANSENT }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop').item.json.nNF }}"}, {"name": "VLFRETE", "value": "={{ $('Loop').item.json.vFrete }}"}, {"name": "DTENT", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "VLST", "value": "={{ $('Loop').item.json.vST }}"}, {"name": "BASEICST", "value": "={{ $('Loop').item.json.BASEICST }}"}, {"name": "VLIPI", "value": "={{ $('Loop').item.json.vIPIDevol }}"}, {"name": "VLPIS", "value": "={{ $('Loop').item.json.vPIS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop').item.json.vCOFINS }}"}, {"name": "CODPAIS", "value": "={{ $('Loop').item.json.cPais }}"}, {"name": "DESCPAIS", "value": "={{ $('Loop').item.json.xPais }}"}, {"name": "CEP", "value": "={{ $('Loop').item.json.CEP }}"}, {"name": "UF", "value": "={{ $('Loop').item.json.ufDest }}"}, {"name": "MUNICIPIO", "value": "={{ $('Loop').item.json.xMun }}"}, {"name": "ENDERECO", "value": "={{ $('Loop').item.json.xLgr }}"}, {"name": "BAIRRO", "value": "={{ $('Loop').item.json.xBairro }}"}, {"name": "VLTOTGER", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "CHAVENFE", "value": "={{ $('Loop').item.json.chNFe }}"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "DTLANCTO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop').item.json.VLBASEIPI }}"}, {"name": "SITUACAONFE", "value": "={{ $('Loop').item.json.cStat }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "PROTOCOLONFE", "value": "={{ $('Loop').item.json.nProt }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop').item.json.ufDest === $('Loop').item.json.ufEmit ? 132 : 232 }}"}, {"name": "CODCONT", "value": "={{ $json.CODCONTFOR }}"}, {"name": "TIPODESCARGA", "value": "6"}, {"name": "CGC", "value": "={{ $('Loop').item.json.CGCENT }}"}, {"name": "CODFORNECNF", "value": "=(SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $('Loop').item.json.CGCENT }}')"}, {"name": "CODFORNEC", "value": "=(SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $('Loop').item.json.CGCENT }}')"}, {"name": "GERANFDEVCLI", "value": "S"}, {"name": "MODELO", "value": "={{ $('Loop').item.json.modelo }}"}, {"name": "CODDEVOL", "value": "={{ $('Loop').item.json.CODDEVOL }}"}, {"name": "EMISSAOPROPRIA", "value": "N"}, {"name": "CODUSURDEVOL", "value": "={{ $('Loop').item.json.CODUSURDEVOL }}"}, {"name": "NUMVIAS", "value": "1"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "OBS", "value": "={{ $('Loop').item.json.infAdProd }}"}, {"name": "OBSNFE", "value": "={{ $('Loop').item.json.infCpl }}"}, {"name": "DTHORAAUTORIZACAOSEFAZ", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop').item.json.dhEmi.split('T')[1].split('-')[0] }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-480, 760], "id": "ae8d5653-2658-42a7-b2e9-3a450d80f089", "name": "PCNFENT", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCESTCOM", "mode": "list", "cachedResultName": "PCESTCOM"}, "valuesToSend": {"values": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "VLDEVOLUCAO", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "VLESTORNO", "value": "0"}, {"name": "DTESTORNO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODUSUR", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "CODFUNC", "value": "1"}, {"name": "NUMTRANSVENDA", "value": "=(SELECT NUMTRANSVENDA FROM PCNFSAID WHERE CHAVENFE = '{{ $('Loop').item.json.refNFe[0] }}')"}, {"name": "HISTORICO", "value": "MERCADO LIVRE"}, {"name": "VLESTORNOCMV", "value": "=(SELECT VLCUSTOFIN FROM PCNFSAID WHERE CHAVENFE = '{{ $('Loop').item.json.refNFe[0] }}')"}, {"name": "CODUSUR2", "value": "0"}, {"name": "CODUSUR3", "value": "0"}, {"name": "CODUSUR4", "value": "0"}, {"name": "VLESTORNO2", "value": "0"}, {"name": "VLESTORNO3", "value": "0"}, {"name": "VLESTORNO4", "value": "0"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-120, 760], "id": "927eae04-0c93-43b6-adf6-a5dee131d94a", "name": "PCESTCOM", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSVENDA, N.NUMNOTA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $('Loop').item.json.refNFe[0] }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [260, 760], "id": "c24969e7-fc08-41e4-bbf8-68b941a1f03f", "name": "Pegar Nº NF Venda", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCCRECLI", "mode": "list", "cachedResultName": "PCCRECLI"}, "valuesToSend": {"values": [{"name": "CODCLI", "value": "={{ $('PROXNUMTRANSENT').item.json[\"CODCLI\"] }}"}, {"name": "DTLANC", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFILIAL", "value": "={{ $('PROXNUMTRANSENT').item.json[\"CODFILIAL\"] }}"}, {"name": "VALOR", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop').item.json.nNF }}"}, {"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "CODFUNC", "value": "1"}, {"name": "HISTORICO", "value": "=CREDITO DEVOLUCAO - REF. NF: {{ $json[\"NUMNOTA\"] }}"}, {"name": "CODFUNCLANC", "value": "1"}, {"name": "NUMERARIO", "value": "N"}, {"name": "CODMOVIMENTO", "value": "250004"}, {"name": "CODROTINA", "value": "618"}, {"name": "NUMCRED", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMCRED\"] }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [460, 760], "id": "9970ad6f-2450-4a87-b864-8802644319c3", "name": "PCCRECLI", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCPREST", "mode": "list", "cachedResultName": "PCPREST"}, "where": {"values": [{"column": "NUMTRANSVENDA", "value": "={{ $('Pegar Nº NF Venda').item.json[\"NUMTRANSVENDA\"] }}"}, {"column": "CODCOB", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"column": "DTPAG", "condition": "IS NULL"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [660, 660], "id": "8f25813f-13de-4a05-b3af-806661ae569c", "name": "Baixar Título com Crédito", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCCRECLI", "mode": "list", "cachedResultName": "PCCRECLI"}, "where": {"values": [{"column": "NUMTRANS", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [860, 580], "id": "759233dd-b5f7-48a5-bd9f-715bfd40929a", "name": "Baixar <PERSON>", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT E.NUMTRANSENT, <PERSON><PERSON><PERSON>AVENFE \n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json.chNFe }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1040, 1020], "id": "642568c8-57c1-4b3e-8de8-9fafce2e2674", "name": "Validar Nota3", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT C.PROXNUMTRANSENT,\n       F.<PERSON>,\n       C.CODCONTCLI,\n       C.CODCONTFOR,\n       F.CODFORNEC,\n       (SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $('Loop1').item.json.CGCFILIAL }}') AS CO<PERSON>ILIAL\n  FROM PCCONSUM C\n    LEFT JOIN PCFORNEC F\n      ON APENASNUMEROS(F.CGC) = '{{ $('Loop1').item.json.CGCENT }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-660, 1000], "id": "4156f200-ba61-4bfe-b974-0f754a2da155", "name": "PROXNUMTRANSENT1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFENT", "mode": "list", "cachedResultName": "PCNFENT"}, "valuesToSend": {"values": [{"name": "DTEMISSAO", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "TIPOEMISSAO", "value": "={{ $('Loop1').item.json.tpImp }}"}, {"name": "FINALIDADENFE", "value": "={{ $('Loop1').item.json.finNFe }}"}, {"name": "SERIE", "value": "={{ $('Loop1').item.json.serie }}"}, {"name": "NUMTRANSENT", "value": "={{ $json.PROXNUMTRANSENT }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop1').item.json.nNF }}"}, {"name": "VLFRETE", "value": "={{ $('Loop1').item.json.vFrete }}"}, {"name": "DTENT", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $('Loop1').item.json.vNF }}"}, {"name": "VLST", "value": "={{ $('Loop1').item.json.vST }}"}, {"name": "BASEICST", "value": "={{ $('Loop1').item.json.BASEICST }}"}, {"name": "VLIPI", "value": "={{ $('Loop1').item.json.vIPI }}"}, {"name": "VLPIS", "value": "={{ $('Loop1').item.json.vPIS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop1').item.json.vCOFINS }}"}, {"name": "CODPAIS", "value": "={{ $('Loop1').item.json.cPais }}"}, {"name": "DESCPAIS", "value": "={{ $('Loop1').item.json.xPais }}"}, {"name": "CEP", "value": "={{ $('Loop1').item.json.CEP }}"}, {"name": "UF", "value": "={{ $('Loop1').item.json.ufDest }}"}, {"name": "MUNICIPIO", "value": "={{ $('Loop1').item.json.xMun }}"}, {"name": "ENDERECO", "value": "={{ $('Loop1').item.json.xLgr }}"}, {"name": "BAIRRO", "value": "={{ $('Loop1').item.json.xBairro }}"}, {"name": "VLTOTGER", "value": "={{ $('Loop1').item.json.vNF }}"}, {"name": "CHAVENFE", "value": "={{ $('Loop1').item.json.chNFe }}"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "DTLANCTO", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop1').item.json.VLBASEIPI }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "SITUACAONFE", "value": "={{ $('Loop1').item.json.cStat }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "PROTOCOLONFE", "value": "={{ $('Loop1').item.json.nProt }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop1').item.json.ufDest === $('Loop1').item.json.ufEmit ? 112 : 212 }}"}, {"name": "CODCONT", "value": "={{ $json.CODCONTFOR }}"}, {"name": "TIPODESCARGA", "value": "R"}, {"name": "CGC", "value": "={{ $('Loop1').item.json.CGCENT }}"}, {"name": "CODFORNEC", "value": "=(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $('Loop1').item.json.CGCENT }}')"}, {"name": "MODELO", "value": "={{ $('Loop1').item.json.modelo }}"}, {"name": "GERANFVENDA", "value": "S"}, {"name": "NUMVIAS", "value": "1"}, {"name": "OBS", "value": "={{ $('Loop1').item.json.infAdProd }}"}, {"name": "OBSNFE", "value": "={{ $('Loop1').item.json.infCpl }}"}, {"name": "DTHORAAUTORIZACAOSEFAZ", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop1').item.json.dhEmi.split('T')[1].split('-')[0] }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-480, 1000], "id": "c9d6f766-9fdb-4aba-9639-5d2bedca5fc6", "name": "PCNFENT1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "devolucao", "operator": {"type": "string", "operation": "contains"}}, {"id": "2119da2d-2e30-4fd5-aa62-1eed63da3296", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "retorno de mercadoria", "operator": {"type": "string", "operation": "contains"}}], "combinator": "or"}, "options": {}}, "id": "79fb9070-0e68-453d-aa7b-0eaea4e07ecb", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1480, 880]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1240, 760], "id": "ee617add-7fc4-4da9-a0ca-0db719a8d75d", "name": "Loop"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1240, 1000], "id": "8a7c3dab-5459-4ae8-8e15-f1c03370584f", "name": "Loop1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT E.NUMTRANSENT, <PERSON><PERSON><PERSON>AVENFE \n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json.chNFe }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1040, 780], "id": "f61e0b14-f50e-4c6e-a76e-34133a4a9ff2", "name": "Validar Nota", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "0", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "182d3199-a371-4425-84a7-2d6ac79cbfe1", "name": "Entrada", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1660, 900]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-860, 780], "id": "71101b8c-4d11-4037-a20a-e5193a1f3db5", "name": "If"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-860, 1020], "id": "ca44812f-cdc9-44ba-8fea-6e4ddfb6bc44", "name": "If1"}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "list", "cachedResultName": "PCNFBASE"}, "valuesToSend": {"values": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT').item.json.PROXNUMTRANSENT }}"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('Loop').item.json.CFOP }}"}, {"name": "VLBASE", "value": "={{ $('Loop').item.json.BASEICMS }}"}, {"name": "VLICMS", "value": "={{ $('Loop').item.json.vICMS }}"}, {"name": "ALIQUOTA", "value": "={{ $('Loop').item.json.pICMS }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-300, 760], "id": "6bc2bd95-9786-4ebc-aff7-118de4f59de8", "name": "PCNFBASE", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "list", "cachedResultName": "PCNFBASE"}, "valuesToSend": {"values": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT1').item.json.PROXNUMTRANSENT }}"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('Loop1').item.json.CFOP }}"}, {"name": "VLBASE", "value": "={{ $('Loop1').item.json.BASEICMS }}"}, {"name": "VLICMS", "value": "={{ $('Loop1').item.json.vICMS }}"}, {"name": "ALIQUOTA", "value": "={{ $('Loop1').item.json.pICMS }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-300, 1000], "id": "4a150620-6dcf-4879-81d8-5d9e1cbface9", "name": "PCNFBASE1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [60, 760], "id": "e2c12765-cf3a-4cc1-ba41-be71cc2f9831", "name": "Atualizar a sequencia", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-120, 1000], "id": "1c7afb01-b419-42e0-92de-c9ec89848ad1", "name": "Atualizar a sequencia1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-2620, 900], "id": "c5cd5b0a-0071-4574-b490-651c8aa056a2", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-2460, 900], "id": "fd057690-05c8-441b-a8c6-158b376ef829", "name": "Extract from File"}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Entrada", "type": "main", "index": 0}]]}, "PROXNUMTRANSENT": {"main": [[{"node": "PCNFENT", "type": "main", "index": 0}]]}, "PCNFENT": {"main": [[{"node": "PCNFBASE", "type": "main", "index": 0}]]}, "PCESTCOM": {"main": [[{"node": "Atualizar a sequencia", "type": "main", "index": 0}]]}, "Pegar Nº NF Venda": {"main": [[{"node": "PCCRECLI", "type": "main", "index": 0}]]}, "PCCRECLI": {"main": [[{"node": "Baixar Título com Crédito", "type": "main", "index": 0}]]}, "Baixar Título com Crédito": {"main": [[{"node": "Baixar <PERSON>", "type": "main", "index": 0}]]}, "Baixar Crédito": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Validar Nota3": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "PROXNUMTRANSENT1": {"main": [[{"node": "PCNFENT1", "type": "main", "index": 0}]]}, "PCNFENT1": {"main": [[{"node": "PCNFBASE1", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "Validar Nota", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Validar Nota3", "type": "main", "index": 0}]]}, "Validar Nota": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Entrada": {"main": [[{"node": "natOp", "type": "main", "index": 0}], []]}, "If": {"main": [[{"node": "PROXNUMTRANSENT", "type": "main", "index": 0}], [{"node": "Loop", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "PROXNUMTRANSENT1", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "PCNFBASE": {"main": [[{"node": "PCESTCOM", "type": "main", "index": 0}]]}, "PCNFBASE1": {"main": [[{"node": "Atualizar a sequencia1", "type": "main", "index": 0}]]}, "Atualizar a sequencia": {"main": [[{"node": "Pegar Nº NF Venda", "type": "main", "index": 0}]]}, "Atualizar a sequencia1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}