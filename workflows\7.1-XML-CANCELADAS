{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-60, 20], "id": "7c0766e6-3855-4f52-bd99-0deed37d0c4a", "name": "When clicking ‘Test workflow’"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [680, 40], "id": "c5c69cba-ab3b-491a-8b2a-11277d49e2a9", "name": "XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 40], "id": "568b4185-fb2a-4097-bfc6-d62e3b5a54be", "name": "Extract from File"}, {"parameters": {"jsCode": "// NOTAS CANCELADAS\nconst items = $('XML').all(); // Obter todos os itens do nó anterior\n\nconst result = [];\n\n// Loop para filtrar notas canceladas e coletar informações adicionais\nitems.forEach(item => {\n  const xmlData = item.json;\n\n  // Verifica se é uma NF cancelada e extrai as informações\n  if (\n    xmlData &&\n    xmlData['procEventoNFe'] &&\n    xmlData['procEventoNFe']['evento'] &&\n    xmlData['procEventoNFe']['evento']['infEvento'] &&\n    xmlData['procEventoNFe']['evento']['infEvento']['chNFe']\n  ) {\n    const infEvento = xmlData['procEventoNFe']['evento']['infEvento'];\n    const retEvento = xmlData['procEventoNFe']['retEvento']['infEvento'];\n\n    // Verifica se o evento é um cancelamento (tipo de evento igual a \"110111\")\n    if (infEvento['tpEvento'] === '110111') {\n      const notaCancelada = {\n        chNFe: infEvento['chNFe'],\n        status: retEvento['cStat'] || 'Status não disponível',\n        statusDescricao: retEvento['xEvento'] || 'Descrição não disponível',\n        data: infEvento['dhEvento'] || 'Data não disponível',\n        CNPJ: infEvento['CNPJ'] || 'CNPJ não disponível',\n        CPF: retEvento['CPFDest'] || 'CPF não disponível',\n        justificativa: infEvento['detEvento']['xJust'] || 'Justificativa não disponível',\n      };\n\n      result.push({ json: notaCancelada });\n    }\n  }\n});\n\nreturn result;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1040, 40], "id": "a3169d14-39e6-4f77-964f-a2382db9dfc2", "name": "Nota <PERSON>"}, {"parameters": {"fileSelector": "//*************/NERVSFLOW/TESTE/CANCELADAS/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [120, 20], "id": "f3ea82db-3705-4395-bb40-206c5e3bcefb", "name": "Ler XML"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT NUMTRANSVENDA AS NUM_TRANSACAO\n  FROM PCNFSAID\n  WHERE DTCANCEL IS NULL\n    AND CHAVENFE = '{{ $json[\"chNFe\"] }}'\n  UNION ALL\nSELECT NUMTRANSENT AS NUM_TRANSACAO\n  FROM PCNFENT\n  WHERE DTCANCEL IS NULL\n    AND CHAVENFE = '{{ $json[\"chNFe\"] }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1220, 40], "id": "eebca320-971e-4c58-8255-58c12e5e8ba7", "name": "Validar Nota", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "e4e50a6e-84a5-49a6-86e7-4bbf9ee5101e", "leftValue": "={{ $json.not_found }}", "rightValue": "={{ $json.fileName }}", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "c2751895-362a-4f5f-8f7e-040852a59ccb", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1400, 40]}, {"parameters": {"assignments": {"assignments": [{"id": "06273527-f453-43d0-aa33-f509788e378c", "name": "fileName", "value": "={{ $('Ler XML').item.json.fileName }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [860, 40], "id": "8f87c469-6f9d-41b8-85f8-1640b49c5087", "name": "fileName"}, {"parameters": {"protocol": "sftp", "operation": "rename", "oldPath": "=/u01/NERVSFLOW/CANCELADAS/{{ $('fileName').item.json.fileName }}", "newPath": "=/u01/NERVSFLOW/CANCELADAS/{{ $('Nota Cancelada').item.json.data.split('T')[0].split('-').reverse().join('') }}/{{ $('fileName').item.json.fileName }}", "options": {}}, "type": "n8n-nodes-base.ftp", "typeVersion": 1, "position": [1620, -60], "id": "3e7246ab-601a-4a88-952e-420eaba8f555", "name": "Mover XML", "credentials": {"sftp": {"id": "av04Vw8ECtsPZXbs", "name": "NERVSFLOW"}}, "onError": "continueErrorOutput"}, {"parameters": {"protocol": "sftp", "operation": "delete", "path": "={{ $json[\"error\"].match(/To: (.+)$/)[1] }}", "options": {}}, "type": "n8n-nodes-base.ftp", "typeVersion": 1, "position": [1820, -20], "id": "f734930c-dada-4065-b137-74308150788b", "name": "Delete XML", "credentials": {"sftp": {"id": "av04Vw8ECtsPZXbs", "name": "NERVSFLOW"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [300, 20], "id": "23d8473b-4d4b-4bcd-906c-ab615c03bc1c", "name": "Loop Over Items"}], "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "fileName", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "Nota Cancelada": {"main": [[{"node": "Validar Nota", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Validar Nota": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Mover XML", "type": "main", "index": 0}]]}, "fileName": {"main": [[{"node": "Nota <PERSON>", "type": "main", "index": 0}]]}, "Mover XML": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "Delete XML", "type": "main", "index": 0}]]}, "Delete XML": {"main": [[{"node": "Mover XML", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Extract from File", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}