{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 941457}, {"functionName": "e.exports", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 939785}, {"functionName": "e.exports", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 946012}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "l.request", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 944042}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 949240}, {"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 593112}, {"functionName": "t.doRequest", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 360018}, {"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 593283}, {"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 458018}, {"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420260}, {"functionName": "onClick", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "13801", "request": {"method": "GET", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/api/nfe/zip/validate?start=********&end=********&sale=all&return=all&full=all&file_types=xml&simple_folder=false", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "myaccount.mercadolivre.com.br"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/invoices/documents/api/nfe/zip/validate?start=********&end=********&sale=all&return=all&full=all&file_types=xml&simple_folder=false"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "cookie", "value": "_ml_ga=GA1.3.*********.**********; _d2id=63e32a96-03c7-4ebc-8128-ebaad376c6a6; _hjSessionUser_720738=********************************************************************************************************************; cookiesPreferencesNotLogged=%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%7D%7D; ftid=bvnNESU9EC4UXMpPL0GMWBb5IwRh7Zje-*************; _fbp=fb.2.*************.*********; _hjSessionUser_580848=********************************************************************************************************************; __gads=ID=2a2f72a9d6970a75:T=1670840631:S=ALNI_MZ9EhSaPHDRxy2aGpnA7iYk-CEMyg; __gpi=UID=000009db7fe0f3e8:T=1670840631:RT=1671793076:S=ALNI_MZMsGHoAOeZGV4XZTANm_W4TcHVqw; orguseridp=*********; orgnickp=ROSELIGUERREIRODOSSANTOS; cookiesPreferencesLoggedFallback=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; ssid=ghy-073106-CCBzynptCH9QhXy1ttw47g2Ns9FBzX-__-*********-__-1785492561042--RRR_0-RRR_0; orguserid=d7dd7tt7tT9H; cp=********; _ml_ga_gid=GA1.3.1069036659.1693390857; _ml_ci=*********.**********; _mldataSessionId=9c60b8c3-28ae-431a-2d48-1a609b601e49; _hjSession_720738=eyJpZCI6IjQ1ZTVjMzFkLTk0ODMtNGFlOC1iYWU2LWU0ODk4ZTA1ODM1YSIsImNyZWF0ZWQiOjE2OTM0OTA5ODAyNTUsImluU2FtcGxlIjpmYWxzZX0=; _hjAbsoluteSessionInProgress=1; _gcl_au=1.1.1992267945.1693491076; _tt_enable_cookie=1; _ttp=WFdgPwkN3TEuFw7lyd90e73ANw_; _pin_unauth=dWlkPU4ySmlOall3WkRZdFpUUTFOeTAwTkRVeExXRm1Zemt0TkRoall6Y3lPV016TXpnMA; cto_bundle=kvwYT18lMkZtVlZrOFZoTHRtSVFCaUltMU93UmI0WDJiQnlFJTJCQWRRYWJwVk0ybnpWMUxiN2g4M0lLemhDSVdjd0dkZFdFMDBLYXFqUk93VHdERnhTYkRDa1RUT3ZoRnZFaUVub2NvTWFBZlUyYTYyaldPM0ROTWJKM256Z1BwYnRpUUh2MFdnRmJGenFBU3FINVVBejlFT3JDOGlHU25wNG8lMkZvR0x4Y1lQbUFRRVJXcUUlM0Q; _hjSession_580848=********************************************************************************************************************; _csrf=l1hHMCIXGdst8jmZBH4XyUq8; _mshops_ga=GA1.3.1261337.**********; _mshops_ga_gid=GA1.3.**********.**********; _gid=GA1.3.**********.**********; _hjSessionUser_783944=********************************************************************************************************************; _hjFirstSeen=1; _hjSession_783944=eyJpZCI6ImQ1MDNhOTdmLTgwYTctNGM1Ni1hNDA1LTRjNTVjMjRlYThmMiIsImNyZWF0ZWQiOjE2OTM0OTExODE0NTYsImluU2FtcGxlIjpmYWxzZX0=; _hjIncludedInSessionSample_580848=1; _ga=GA1.1.*********.**********; _uetsid=f05394e0471e11eebbd17378f95757d8; _uetvid=04da5180722e11ed866bcf4b7f150dab; _ga_NDJFKMJ2PD=GS1.1.**********.214.1.**********.59.0.0; _hjShownFeedbackMessage=true; cookiesPreferencesLogged=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D"}, {"name": "device-memory", "value": "8"}, {"name": "downlink", "value": "10"}, {"name": "dpr", "value": "1"}, {"name": "ect", "value": "4g"}, {"name": "if-none-match", "value": "W/\"f-M44wpu/0uQc80d9aNEVqZ9p98lY\""}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "rtt", "value": "50"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "viewport-width", "value": "1366"}, {"name": "x-csrf-token", "value": "coSTEwdW-NeSQiqimEVFe2a0HaffpZB9wcEI"}, {"name": "x-flow-starter", "value": "true"}, {"name": "x-newrelic-id", "value": "XQ4OVF5VGwEBVllbAgcFUw=="}, {"name": "x-request-id", "value": "ac9c1343-1356-4d96-a666-f280bcde3c24"}], "queryString": [{"name": "start", "value": "********"}, {"name": "end", "value": "********"}, {"name": "sale", "value": "all"}, {"name": "return", "value": "all"}, {"name": "full", "value": "all"}, {"name": "file_types", "value": "xml"}, {"name": "simple_folder", "value": "false"}], "cookies": [{"name": "_ml_ga", "value": "GA1.3.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:26:14.559Z", "httpOnly": false, "secure": false}, {"name": "_d2id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:41:03.369Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSessionUser_720738", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:21:04.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesNotLogged", "value": "%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-12-02T16:29:53.821Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ftid", "value": "bvnNESU9EC4UXMpPL0GMWBb5IwRh7Zje-*************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_fbp", "value": "fb.2.*************.*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-29T14:13:00.000Z", "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_hjSessionUser_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:26:14.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "__gads", "value": "ID=2a2f72a9d6970a75:T=1670840631:S=ALNI_MZ9EhSaPHDRxy2aGpnA7iYk-CEMyg", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:23:51.000Z", "httpOnly": false, "secure": false}, {"name": "__gpi", "value": "UID=000009db7fe0f3e8:T=1670840631:RT=1671793076:S=ALNI_MZMsGHoAOeZGV4XZTANm_W4TcHVqw", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:23:51.000Z", "httpOnly": false, "secure": false}, {"name": "orguseridp", "value": "*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orgnickp", "value": "ROSELIGUERREIRODOSSANTOS", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged<PERSON><PERSON>back", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-07T14:26:14.163Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ssid", "value": "ghy-073106-CCBzynptCH9QhXy1ttw47g2Ns9FBzX-__-*********-__-1785492561042--RRR_0-RRR_0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "orguserid", "value": "d7dd7tt7tT9H", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cp", "value": "********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-20T10:17:02.610Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_ml_ga_gid", "value": "GA1.3.1069036659.1693390857", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:26:14.000Z", "httpOnly": false, "secure": false}, {"name": "_ml_ci", "value": "*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_mldataSessionId", "value": "9c60b8c3-28ae-431a-2d48-1a609b601e49", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:56:14.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_720738", "value": "eyJpZCI6IjQ1ZTVjMzFkLTk0ODMtNGFlOC1iYWU2LWU0ODk4ZTA1ODM1YSIsImNyZWF0ZWQiOjE2OTM0OTA5ODAyNTUsImluU2FtcGxlIjpmYWxzZX0=", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:43:38.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjAbsoluteSessionInProgress", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_gcl_au", "value": "1.1.1992267945.1693491076", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-29T14:11:15.000Z", "httpOnly": false, "secure": false}, {"name": "_tt_enable_cookie", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "_ttp", "value": "WFdgPwkN3TEuFw7lyd90e73ANw_", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "_pin_unauth", "value": "dWlkPU4ySmlOall3WkRZdFpUUTFOeTAwTkRVeExXRm1Zemt0TkRoall6Y3lPV016TXpnMA", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "cto_bundle", "value": "kvwYT18lMkZtVlZrOFZoTHRtSVFCaUltMU93UmI0WDJiQnlFJTJCQWRRYWJwVk0ybnpWMUxiN2g4M0lLemhDSVdjd0dkZFdFMDBLYXFqUk93VHdERnhTYkRDa1RUT3ZoRnZFaUVub2NvTWFBZlUyYTYyaldPM0ROTWJKM256Z1BwYnRpUUh2MFdnRmJGenFBU3FINVVBejlFT3JDOGlHU25wNG8lMkZvR0x4Y1lQbUFRRVJXcUUlM0Q", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-30T00:11:17.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSession_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_csrf", "value": "l1hHMCIXGdst8jmZBH4XyUq8", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true}, {"name": "_mshops_ga", "value": "GA1.3.1261337.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:12:32.208Z", "httpOnly": false, "secure": false}, {"name": "_mshops_ga_gid", "value": "GA1.3.**********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:12:32.000Z", "httpOnly": false, "secure": false}, {"name": "_gid", "value": "GA1.3.**********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:12:59.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSessionUser_783944", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:13:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjFirstSeen", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_783944", "value": "eyJpZCI6ImQ1MDNhOTdmLTgwYTctNGM1Ni1hNDA1LTRjNTVjMjRlYThmMiIsImNyZWF0ZWQiOjE2OTM0OTExODE0NTYsImluU2FtcGxlIjpmYWxzZX0=", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:43:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjIncludedInSessionSample_580848", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:29:17.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ga", "value": "GA1.1.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:21:13.589Z", "httpOnly": false, "secure": false}, {"name": "_uetsid", "value": "f05394e0471e11eebbd17378f95757d8", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:21:13.000Z", "httpOnly": false, "secure": false}, {"name": "_uetvid", "value": "04da5180722e11ed866bcf4b7f150dab", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:21:13.000Z", "httpOnly": false, "secure": false}, {"name": "_ga_NDJFKMJ2PD", "value": "GS1.1.**********.214.1.**********.59.0.0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:21:14.723Z", "httpOnly": false, "secure": false}, {"name": "_hjShownFeedbackMessage", "value": "true", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "2023-09-01T14:21:15.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:31:14.163Z", "httpOnly": false, "secure": true, "sameSite": "None"}], "headersSize": -1, "bodySize": 0}, "response": {"status": 304, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "accept-ch", "value": "device-memory, dpr, viewport-width, rtt, downlink, ect, save-data"}, {"name": "accept-ch-lifetime", "value": "60"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:16 GMT"}, {"name": "etag", "value": "W/\"f-M44wpu/0uQc80d9aNEVqZ9p98lY\""}, {"name": "expect-ct", "value": "max-age=0"}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "strict-transport-security", "value": "max-age=15552000; includeSubDomains"}, {"name": "via", "value": "1.1 75e7f56ac0cd014270ac3a4272f3830e.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "1c7eNpSvHlmsazMn0RRIA5KjvhDTBYniC10uoR9PmJGuoFHkzJ21dA=="}, {"name": "x-amz-cf-pop", "value": "GRU3-P1"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-d2id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6"}, {"name": "x-dns-prefetch-control", "value": "on"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-envoy-upstream-service-time", "value": "839"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-request-device-id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6"}, {"name": "x-request-id", "value": "3f899834-2a8a-4c42-880e-09f3053e22bd"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 15, "mimeType": "application/json", "text": "{\"empty\":false}"}, "redirectURL": "", "headersSize": -1, "bodySize": 0, "_transferSize": 690, "_error": null}, "serverIPAddress": "***************", "startedDateTime": "2023-08-31T14:27:43.832Z", "time": 974.8089999193326, "timings": {"blocked": 8.664999949373305, "dns": -1, "ssl": -1, "connect": -1, "send": 0.6699999999999999, "wait": 962.8009999890998, "receive": 2.6729999808594584, "_blocked_queueing": 1.1399999493733048}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 421558}, {"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 421464}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420738}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420275}, {"functionName": "onClick", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}, "_priority": "VeryHigh", "_resourceType": "document", "cache": {}, "connection": "13801", "request": {"method": "GET", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe/download?start=********&end=********&sale=all&return=all&full=all&file_types=xml&simple_folder=false", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "myaccount.mercadolivre.com.br"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/invoices/documents/nfe/download?start=********&end=********&sale=all&return=all&full=all&file_types=xml&simple_folder=false"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "cookie", "value": "_ml_ga=GA1.3.*********.**********; _d2id=63e32a96-03c7-4ebc-8128-ebaad376c6a6; _hjSessionUser_720738=********************************************************************************************************************; cookiesPreferencesNotLogged=%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%7D%7D; ftid=bvnNESU9EC4UXMpPL0GMWBb5IwRh7Zje-*************; _fbp=fb.2.*************.*********; _hjSessionUser_580848=********************************************************************************************************************; __gads=ID=2a2f72a9d6970a75:T=1670840631:S=ALNI_MZ9EhSaPHDRxy2aGpnA7iYk-CEMyg; __gpi=UID=000009db7fe0f3e8:T=1670840631:RT=1671793076:S=ALNI_MZMsGHoAOeZGV4XZTANm_W4TcHVqw; orguseridp=*********; orgnickp=ROSELIGUERREIRODOSSANTOS; cookiesPreferencesLoggedFallback=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; ssid=ghy-073106-CCBzynptCH9QhXy1ttw47g2Ns9FBzX-__-*********-__-1785492561042--RRR_0-RRR_0; orguserid=d7dd7tt7tT9H; cp=********; _ml_ga_gid=GA1.3.1069036659.1693390857; _ml_ci=*********.**********; _mldataSessionId=9c60b8c3-28ae-431a-2d48-1a609b601e49; _hjSession_720738=eyJpZCI6IjQ1ZTVjMzFkLTk0ODMtNGFlOC1iYWU2LWU0ODk4ZTA1ODM1YSIsImNyZWF0ZWQiOjE2OTM0OTA5ODAyNTUsImluU2FtcGxlIjpmYWxzZX0=; _hjAbsoluteSessionInProgress=1; _gcl_au=1.1.1992267945.1693491076; _tt_enable_cookie=1; _ttp=WFdgPwkN3TEuFw7lyd90e73ANw_; _pin_unauth=dWlkPU4ySmlOall3WkRZdFpUUTFOeTAwTkRVeExXRm1Zemt0TkRoall6Y3lPV016TXpnMA; cto_bundle=kvwYT18lMkZtVlZrOFZoTHRtSVFCaUltMU93UmI0WDJiQnlFJTJCQWRRYWJwVk0ybnpWMUxiN2g4M0lLemhDSVdjd0dkZFdFMDBLYXFqUk93VHdERnhTYkRDa1RUT3ZoRnZFaUVub2NvTWFBZlUyYTYyaldPM0ROTWJKM256Z1BwYnRpUUh2MFdnRmJGenFBU3FINVVBejlFT3JDOGlHU25wNG8lMkZvR0x4Y1lQbUFRRVJXcUUlM0Q; _hjSession_580848=********************************************************************************************************************; _csrf=l1hHMCIXGdst8jmZBH4XyUq8; _mshops_ga=GA1.3.1261337.**********; _mshops_ga_gid=GA1.3.**********.**********; _gid=GA1.3.**********.**********; _hjSessionUser_783944=********************************************************************************************************************; _hjFirstSeen=1; _hjSession_783944=eyJpZCI6ImQ1MDNhOTdmLTgwYTctNGM1Ni1hNDA1LTRjNTVjMjRlYThmMiIsImNyZWF0ZWQiOjE2OTM0OTExODE0NTYsImluU2FtcGxlIjpmYWxzZX0=; _hjIncludedInSessionSample_580848=1; _ga=GA1.1.*********.**********; _uetsid=f05394e0471e11eebbd17378f95757d8; _uetvid=04da5180722e11ed866bcf4b7f150dab; _ga_NDJFKMJ2PD=GS1.1.**********.214.1.**********.59.0.0; _hjShownFeedbackMessage=true; cookiesPreferencesLogged=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D"}, {"name": "device-memory", "value": "8"}, {"name": "downlink", "value": "10"}, {"name": "dpr", "value": "1"}, {"name": "ect", "value": "4g"}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "rtt", "value": "50"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "document"}, {"name": "sec-fetch-mode", "value": "navigate"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "sec-fetch-user", "value": "?1"}, {"name": "upgrade-insecure-requests", "value": "1"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "viewport-width", "value": "1366"}], "queryString": [{"name": "start", "value": "********"}, {"name": "end", "value": "********"}, {"name": "sale", "value": "all"}, {"name": "return", "value": "all"}, {"name": "full", "value": "all"}, {"name": "file_types", "value": "xml"}, {"name": "simple_folder", "value": "false"}], "cookies": [{"name": "_ml_ga", "value": "GA1.3.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:26:14.559Z", "httpOnly": false, "secure": false}, {"name": "_d2id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:41:03.369Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSessionUser_720738", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:21:04.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesNotLogged", "value": "%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-12-02T16:29:53.821Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ftid", "value": "bvnNESU9EC4UXMpPL0GMWBb5IwRh7Zje-*************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_fbp", "value": "fb.2.*************.*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-29T14:13:00.000Z", "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_hjSessionUser_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:26:14.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "__gads", "value": "ID=2a2f72a9d6970a75:T=1670840631:S=ALNI_MZ9EhSaPHDRxy2aGpnA7iYk-CEMyg", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:23:51.000Z", "httpOnly": false, "secure": false}, {"name": "__gpi", "value": "UID=000009db7fe0f3e8:T=1670840631:RT=1671793076:S=ALNI_MZMsGHoAOeZGV4XZTANm_W4TcHVqw", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-01-06T10:23:51.000Z", "httpOnly": false, "secure": false}, {"name": "orguseridp", "value": "*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orgnickp", "value": "ROSELIGUERREIRODOSSANTOS", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged<PERSON><PERSON>back", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-07T14:26:14.163Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ssid", "value": "ghy-073106-CCBzynptCH9QhXy1ttw47g2Ns9FBzX-__-*********-__-1785492561042--RRR_0-RRR_0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "orguserid", "value": "d7dd7tt7tT9H", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T10:09:36.818Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cp", "value": "********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-20T10:17:02.610Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_ml_ga_gid", "value": "GA1.3.1069036659.1693390857", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:26:14.000Z", "httpOnly": false, "secure": false}, {"name": "_ml_ci", "value": "*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_mldataSessionId", "value": "9c60b8c3-28ae-431a-2d48-1a609b601e49", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:44.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_720738", "value": "eyJpZCI6IjQ1ZTVjMzFkLTk0ODMtNGFlOC1iYWU2LWU0ODk4ZTA1ODM1YSIsImNyZWF0ZWQiOjE2OTM0OTA5ODAyNTUsImluU2FtcGxlIjpmYWxzZX0=", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:43:38.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjAbsoluteSessionInProgress", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_gcl_au", "value": "1.1.1992267945.1693491076", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-29T14:11:15.000Z", "httpOnly": false, "secure": false}, {"name": "_tt_enable_cookie", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "_ttp", "value": "WFdgPwkN3TEuFw7lyd90e73ANw_", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "_pin_unauth", "value": "dWlkPU4ySmlOall3WkRZdFpUUTFOeTAwTkRVeExXRm1Zemt0TkRoall6Y3lPV016TXpnMA", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:11:16.000Z", "httpOnly": false, "secure": false}, {"name": "cto_bundle", "value": "kvwYT18lMkZtVlZrOFZoTHRtSVFCaUltMU93UmI0WDJiQnlFJTJCQWRRYWJwVk0ybnpWMUxiN2g4M0lLemhDSVdjd0dkZFdFMDBLYXFqUk93VHdERnhTYkRDa1RUT3ZoRnZFaUVub2NvTWFBZlUyYTYyaldPM0ROTWJKM256Z1BwYnRpUUh2MFdnRmJGenFBU3FINVVBejlFT3JDOGlHU25wNG8lMkZvR0x4Y1lQbUFRRVJXcUUlM0Q", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-30T00:11:17.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSession_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_csrf", "value": "l1hHMCIXGdst8jmZBH4XyUq8", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true}, {"name": "_mshops_ga", "value": "GA1.3.1261337.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:12:32.208Z", "httpOnly": false, "secure": false}, {"name": "_mshops_ga_gid", "value": "GA1.3.**********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:12:32.000Z", "httpOnly": false, "secure": false}, {"name": "_gid", "value": "GA1.3.**********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:12:59.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSessionUser_783944", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T14:13:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjFirstSeen", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:57:24.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_783944", "value": "eyJpZCI6ImQ1MDNhOTdmLTgwYTctNGM1Ni1hNDA1LTRjNTVjMjRlYThmMiIsImNyZWF0ZWQiOjE2OTM0OTExODE0NTYsImluU2FtcGxlIjpmYWxzZX0=", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:43:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjIncludedInSessionSample_580848", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:29:17.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ga", "value": "GA1.1.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:21:13.589Z", "httpOnly": false, "secure": false}, {"name": "_uetsid", "value": "f05394e0471e11eebbd17378f95757d8", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-01T14:21:13.000Z", "httpOnly": false, "secure": false}, {"name": "_uetvid", "value": "04da5180722e11ed866bcf4b7f150dab", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-24T14:21:13.000Z", "httpOnly": false, "secure": false}, {"name": "_ga_NDJFKMJ2PD", "value": "GS1.1.**********.214.1.**********.59.0.0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-04T14:21:14.723Z", "httpOnly": false, "secure": false}, {"name": "_hjShownFeedbackMessage", "value": "true", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "2023-09-01T14:21:15.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-08-31T14:31:14.163Z", "httpOnly": false, "secure": true, "sameSite": "None"}], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "accept-ch", "value": "device-memory, dpr, viewport-width, rtt, downlink, ect, save-data"}, {"name": "accept-ch-lifetime", "value": "60"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "cache-control", "value": "private, max-age=0, no-cache, no-store, must-revalidate"}, {"name": "content-disposition", "value": "inline; filename=MIMOIMPEEXPLTDA_********_********.zip"}, {"name": "content-type", "value": "application/zip"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:17 GMT"}, {"name": "expect-ct", "value": "max-age=0"}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "strict-transport-security", "value": "max-age=15552000; includeSubDomains"}, {"name": "via", "value": "1.1 75e7f56ac0cd014270ac3a4272f3830e.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "9-H9Tj7BI9evTEH0dJ2EoDZ6Cu0ixLZU40Q9-X9MuyIfvk0xR80FOw=="}, {"name": "x-amz-cf-pop", "value": "GRU3-P1"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-d2id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6"}, {"name": "x-dns-prefetch-control", "value": "on"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-envoy-upstream-service-time", "value": "468"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-request-device-id", "value": "63e32a96-03c7-4ebc-8128-ebaad376c6a6"}, {"name": "x-request-id", "value": "8846caa0-3b19-473e-b5d3-5112c265b733"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 0, "mimeType": "application/zip"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 32684, "_error": "net::ERR_ABORTED"}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:44.827Z", "time": 645.8700000075623, "timings": {"blocked": 33.7770000519827, "dns": -1, "ssl": -1, "connect": -1, "send": 0.490000000000002, "wait": 608.7759999662414, "receive": 2.826999989338219, "_blocked_queueing": 3.141000051982701}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "i", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 15518}, {"functionName": "doSendTrack", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 19011}, {"functionName": "", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 18741}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "doSend", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 18717}, {"functionName": "", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 17001}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "sendTrackFromBrowser", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 16934}, {"functionName": "send", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 17488}, {"functionName": "c", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 21341}, {"functionName": "r.send", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 21956}, {"functionName": "e.<computed>", "scriptId": "126", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 24730}, {"functionName": "t.default", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 356981}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420438}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "value", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420275}, {"functionName": "onClick", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "134", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "13668", "request": {"method": "POST", "url": "https://api.mercadolibre.com/tracks", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "api.mercadolibre.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/tracks"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "content-length", "value": "4487"}, {"name": "content-type", "value": "text/plain;charset=UTF-8"}, {"name": "origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 4487, "postData": {"mimeType": "text/plain;charset=UTF-8", "text": "{\"tracks\":[{\"path\":\"/myml/invoices/documents/nfe/download/validate/empty\",\"user\":{\"uid\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"user_id\":*********,\"root_user_id\":*********,\"user_tags\":[\"operator\"],\"privacy_consents\":{\"advertising\":true,\"traceability\":true,\"performance\":true,\"functionality\":true},\"session_id\":\"9c60b8c3-28ae-431a-2d48-1a609b601e49\"},\"type\":\"event\",\"user_local_timestamp\":\"2023-08-31T11:27:44.809-0300\",\"server_id\":\"bf0f9ef8-0e7d-43f3-8712-d2919795cf75\",\"id\":\"46fb767a-8b95-49c3-af26-082108bbf4b4\",\"event_data\":{\"empty\":false,\"file_types\":\"xml\",\"simple_folder\":false,\"end\":\"********\",\"start\":\"********\",\"sale\":\"all\",\"returns\":\"all\",\"full\":\"all\",\"others\":\"\"},\"platform\":{\"http\":{\"cookies\":{\"_d2id\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"_fbp\":\"fb.2.*************.*********\",\"cp\":\"********\",\"orguseridp\":\"*********\"},\"headers\":{\"host\":\"myaccount.mercadolivre.com.br\",\"x-request-id\":\"433ee680-f0e1-4cb6-838d-33448ef74451\",\"x-device-js\":\"true\",\"x-d2id\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"x-platform\":\"ml\"},\"http_url\":\"https://myaccount.mercadolivre.com.br/invoices/documents/nfe\",\"http_referer\":\"https://myaccount.mercadolivre.com.br/invoices/documents/type\",\"utm\":{},\"intersection_observer_supported\":true}},\"application\":{\"sdk_version\":{\"node_version\":\"4.7.4\",\"js_version\":\"0.4.0\"},\"version\":\"3.6.3\",\"business\":\"mercadolibre\",\"site_id\":\"MLB\",\"server_hostname\":\"\",\"server_poolname\":\"\",\"app_name\":\"\",\"secured\":{\"encrypted\":\"2c026f18f694e5a0dcbb3d8e624139a4:ac31cd2770863737a5976fa6eb6eca6a2d4e1876b9f680b8b39a10971b304bb2b232d750e3f401f98cc5f97fac6a8d82f5a743c13eb28cdc75858a55f69ec48b0198d3d16a39d9280c3fbe28418d7149b00a6028425e7f87d883a7b1081e6f64518ffeab8771ede6695421d7dd56869219a8eb4fefcd2aff42dce5e36d02727c750c1b36d6e41806ee9d942a115b5b5043857b37d060a661\",\"signature\":\"eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.6tnSsMkq6LW6i0K9TvKwz8B2qMlavniNT_nGxCNiD4I\"}},\"device\":{\"resolution_height\":728,\"resolution_width\":1366,\"platform\":\"/web/desktop\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"},\"experiments\":{},\"created_hidden\":false},{\"path\":\"/myml/invoices/documents/nfe/download/start\",\"user\":{\"uid\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"user_id\":*********,\"root_user_id\":*********,\"user_tags\":[\"operator\"],\"privacy_consents\":{\"advertising\":true,\"traceability\":true,\"performance\":true,\"functionality\":true},\"session_id\":\"9c60b8c3-28ae-431a-2d48-1a609b601e49\"},\"type\":\"event\",\"user_local_timestamp\":\"2023-08-31T11:27:44.811-0300\",\"server_id\":\"bf0f9ef8-0e7d-43f3-8712-d2919795cf75\",\"id\":\"33464d0f-6939-44d5-a40c-b68509af0760\",\"event_data\":{\"file_types\":\"xml\",\"simple_folder\":false,\"end\":\"********\",\"start\":\"********\",\"sale\":\"all\",\"returns\":\"all\",\"full\":\"all\",\"others\":\"\"},\"platform\":{\"http\":{\"cookies\":{\"_d2id\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"_fbp\":\"fb.2.*************.*********\",\"cp\":\"********\",\"orguseridp\":\"*********\"},\"headers\":{\"host\":\"myaccount.mercadolivre.com.br\",\"x-request-id\":\"433ee680-f0e1-4cb6-838d-33448ef74451\",\"x-device-js\":\"true\",\"x-d2id\":\"63e32a96-03c7-4ebc-8128-ebaad376c6a6\",\"x-platform\":\"ml\"},\"http_url\":\"https://myaccount.mercadolivre.com.br/invoices/documents/nfe\",\"http_referer\":\"https://myaccount.mercadolivre.com.br/invoices/documents/type\",\"utm\":{},\"intersection_observer_supported\":true}},\"application\":{\"sdk_version\":{\"node_version\":\"4.7.4\",\"js_version\":\"0.4.0\"},\"version\":\"3.6.3\",\"business\":\"mercadolibre\",\"site_id\":\"MLB\",\"server_hostname\":\"\",\"server_poolname\":\"\",\"app_name\":\"\",\"secured\":{\"encrypted\":\"2c026f18f694e5a0dcbb3d8e624139a4:ac31cd2770863737a5976fa6eb6eca6a2d4e1876b9f680b8b39a10971b304bb2b232d750e3f401f98cc5f97fac6a8d82f5a743c13eb28cdc75858a55f69ec48b0198d3d16a39d9280c3fbe28418d7149b00a6028425e7f87d883a7b1081e6f64518ffeab8771ede6695421d7dd56869219a8eb4fefcd2aff42dce5e36d02727c750c1b36d6e41806ee9d942a115b5b5043857b37d060a661\",\"signature\":\"eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.6tnSsMkq6LW6i0K9TvKwz8B2qMlavniNT_nGxCNiD4I\"}},\"device\":{\"resolution_height\":728,\"resolution_width\":1366,\"platform\":\"/web/desktop\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"},\"experiments\":{},\"created_hidden\":false}]}"}}, "response": {"status": 200, "statusText": "", "httpVersion": "http/2.0", "headers": [{"name": "access-control-allow-headers", "value": "Content-Type"}, {"name": "access-control-allow-methods", "value": "PUT, GET, POST, DELETE, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "access-control-max-age", "value": "86400"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:17 GMT"}, {"name": "vary", "value": "Accept-Encoding, User-Agent"}, {"name": "via", "value": "1.1 2ab6e579c97781986b5aa98f1cda00da.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "zHetUIIbxgAs2I-mV49YUAVsymOAoz9ZcrAP6QEX7afaWCjSq2dLhA=="}, {"name": "x-amz-cf-pop", "value": "GRU3-P3"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-request-id", "value": "2237836b-d3fc-4ae2-9051-509544cd9b47"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2208, "mimeType": "application/json", "text": "{\"records\":[{\"status\":200},{\"status\":200}],\"configuration\":{\"batch_size\":40,\"batch_size_wifi\":80,\"blocklist\":\"[\\\"/cards/nfc/constraint/update/\\\",\\\"/cards/nfc/core/service/error\\\",\\\"/auth/attestation/nonce/fail\\\",\\\"/cross_app_links/fetch_time\\\",\\\"/cards/nfc/feature/availability\\\",\\\"cards/nfc/feature/availability\\\"]\",\"blocklistV2\":\"[{\\\"path\\\": \\\"/cards/nfc/constraint/update/\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cards/nfc/core/service/error\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/auth/attestation/nonce/fail\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.201.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cross_app_links/fetch_time\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cross_app_links/fetch_time\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.196.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cards/nfc/feature/availability\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"2.256.0\\\"},{\\\"path\\\": \\\"cards/nfc/feature/availability\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"2.256.0\\\"},{\\\"path\\\": \\\"/navigation/link_monitoring\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.201.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/navigation/link_monitoring\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/sign_in/sso\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/flow/friction/networking\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/flow/friction/system\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"}]\",\"bulk_size\":10,\"bulk_size_wifi\":20,\"database_status_check_interval\":30,\"experiment_storage_enabled\":\"true\",\"geo_track_enabled_android_version\":\"8.4.0\",\"geo_track_enabled_ios_version\":\"0.15.0\",\"geolocation_max_hours\":4,\"pagination_enabled_android_version\":\"8.0.0\",\"pagination_enabled_ios_version\":\"10.0.0\",\"pagination_page_size\":50,\"scheduled_dispatch_interval\":5},\"failed_record_count\":0}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 1071, "_error": null}, "serverIPAddress": "***************", "startedDateTime": "2023-08-31T14:27:44.857Z", "time": 154.*************, "timings": {"blocked": 1.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 151.**************, "receive": 0.****************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=391507&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "620"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "391507"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}], "cookies": [], "headersSize": 938, "bodySize": 620, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":308},\"rxSize\":{\"t\":24},\"duration\":{\"t\":172},\"cbTime\":{\"t\":0},\"time\":{\"t\":381492}}},{\"params\":{\"method\":\"GET\",\"hostname\":\"myaccount.mercadolivre.com.br\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"myaccount.mercadolivre.com.br:443\",\"pathname\":\"/invoices/documents/api/nfe/zip/validate\",\"status\":200},\"metrics\":{\"count\":1,\"rxSize\":{\"t\":15},\"duration\":{\"t\":1014},\"cbTime\":{\"t\":0},\"time\":{\"t\":386962}}}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:20 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 327, "bodySize": 24, "_transferSize": 351, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:48.377Z", "time": 168.**************, "timings": {"blocked": 1.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 165.*************, "receive": 1.***************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3981}, {"functionName": "i.length.a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1545}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15495", "request": {"method": "POST", "url": "https://bam.nr-data.net/events/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=391509&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "114"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "391509"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}], "cookies": [], "headersSize": 936, "bodySize": 114, "postData": {"mimeType": "text/plain", "text": "bel.7;2,,8aky,r5,,,'GET,5k,'myaccount.mercadolivre.com.br:443,'/invoices/documents/api/nfe/zip/validate,,f,,'0,!!!"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "close"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:20 GMT"}, {"name": "x-served-by", "value": "cache-cgh11145-CGH"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 322, "bodySize": 24, "_transferSize": 346, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:48.381Z", "time": 170.**************, "timings": {"blocked": 2.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************2, "wait": 167.**************, "receive": 0.***************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/resources/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=392509&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a&st=*************", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "27439"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "392509"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}, {"name": "st", "value": "*************"}], "cookies": [], "headersSize": 958, "bodySize": 27439, "postData": {"mimeType": "text/plain", "text": "{\"res\":[{\"n\":\"load\",\"s\":352646,\"e\":352646,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":352646,\"e\":352646,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":352646,\"e\":352646,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":361638,\"e\":361638,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":361638,\"e\":361638,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":361638,\"e\":361638,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":371665,\"e\":371665,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":371665,\"e\":371666,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":371666,\"e\":371666,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":381664,\"e\":381664,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":381664,\"e\":381664,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":381664,\"e\":381664,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":387976,\"e\":387976,\"t\":\"event\",\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\"},{\"n\":\"load\",\"s\":387976,\"e\":387976,\"t\":\"event\",\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\"},{\"n\":\"load\",\"s\":391678,\"e\":391678,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":391678,\"e\":391678,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":391678,\"e\":391678,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":391684,\"e\":391684,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/events/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":391684,\"e\":391684,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/events/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":391684,\"e\":391684,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/events/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"Ajax\",\"s\":352470,\"e\":352646,\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":361466,\"e\":361638,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":371483,\"e\":371666,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":381492,\"e\":381664,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":386962,\"e\":387976,\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":391507,\"e\":391678,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":391511,\"e\":391684,\"o\":\"200 POST: bam.nr-data.net:443/events/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"xmlhttprequest\",\"s\":352470,\"e\":352644,\"o\":\"https://bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":361466,\"e\":361636,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":371483,\"e\":371664,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":381492,\"e\":381662,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":386962,\"e\":387937,\"o\":\"https://myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":391507,\"e\":391676,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":391511,\"e\":391681,\"o\":\"https://bam.nr-data.net:443/events/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"pointerover\",\"s\":386111,\"e\":386111,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":386111,\"e\":386111,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerover\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerover\",\"s\":386966,\"e\":386966,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointerover\",\"s\":386966,\"e\":386966,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"mousing\",\"s\":386111,\"e\":386288,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"mousing\",\"s\":386288,\"e\":386966,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"mousing\",\"s\":386966,\"e\":387278,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":386111,\"e\":386111,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386111,\"e\":386111,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386111,\"e\":386111,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386112,\"e\":386112,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386112,\"e\":386112,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386112,\"e\":386112,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386119,\"e\":386119,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386119,\"e\":386119,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386119,\"e\":386119,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386127,\"e\":386127,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386127,\"e\":386127,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386127,\"e\":386127,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386135,\"e\":386135,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386135,\"e\":386135,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386135,\"e\":386135,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386143,\"e\":386143,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386143,\"e\":386143,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386143,\"e\":386143,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386151,\"e\":386151,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386151,\"e\":386151,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386151,\"e\":386151,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386159,\"e\":386159,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386159,\"e\":386159,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386159,\"e\":386159,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386167,\"e\":386167,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386167,\"e\":386167,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386167,\"e\":386167,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386175,\"e\":386175,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386175,\"e\":386175,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386175,\"e\":386175,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386183,\"e\":386183,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386183,\"e\":386183,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386183,\"e\":386183,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386199,\"e\":386199,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386199,\"e\":386199,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386199,\"e\":386200,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386207,\"e\":386207,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386207,\"e\":386207,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386207,\"e\":386207,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386215,\"e\":386215,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386215,\"e\":386215,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386215,\"e\":386215,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386222,\"e\":386222,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386222,\"e\":386222,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386222,\"e\":386222,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386231,\"e\":386231,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386231,\"e\":386231,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386231,\"e\":386231,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386247,\"e\":386247,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386247,\"e\":386247,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386247,\"e\":386247,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386254,\"e\":386254,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386254,\"e\":386254,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386254,\"e\":386254,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386263,\"e\":386263,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386263,\"e\":386263,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386263,\"e\":386263,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386270,\"e\":386270,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386271,\"e\":386271,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386271,\"e\":386271,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386279,\"e\":386279,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386279,\"e\":386279,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386279,\"e\":386279,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386289,\"e\":386289,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386294,\"e\":386294,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386294,\"e\":386294,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386294,\"e\":386294,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386303,\"e\":386304,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386304,\"e\":386304,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386304,\"e\":386304,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386309,\"e\":386309,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386310,\"e\":386310,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386310,\"e\":386310,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386319,\"e\":386319,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386319,\"e\":386319,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386319,\"e\":386319,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386325,\"e\":386325,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386326,\"e\":386326,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386326,\"e\":386326,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386334,\"e\":386334,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386334,\"e\":386334,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386334,\"e\":386334,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386343,\"e\":386343,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386343,\"e\":386343,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386343,\"e\":386343,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386350,\"e\":386350,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386350,\"e\":386350,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386350,\"e\":386350,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386359,\"e\":386359,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386359,\"e\":386359,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386359,\"e\":386359,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386383,\"e\":386383,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386383,\"e\":386383,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386383,\"e\":386383,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386399,\"e\":386399,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386399,\"e\":386399,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386399,\"e\":386399,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386470,\"e\":386471,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386471,\"e\":386471,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":386471,\"e\":386471,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":387102,\"e\":387102,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387102,\"e\":387102,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387102,\"e\":387102,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387141,\"e\":387141,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387142,\"e\":387142,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387142,\"e\":387142,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387166,\"e\":387166,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387166,\"e\":387166,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387166,\"e\":387166,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387173,\"e\":387174,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387174,\"e\":387174,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387174,\"e\":387174,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387181,\"e\":387181,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387181,\"e\":387182,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387182,\"e\":387182,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387189,\"e\":387189,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387189,\"e\":387189,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387189,\"e\":387189,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387198,\"e\":387198,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387198,\"e\":387198,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387198,\"e\":387198,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387206,\"e\":387206,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387206,\"e\":387206,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387206,\"e\":387206,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387213,\"e\":387213,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387213,\"e\":387214,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387214,\"e\":387214,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387222,\"e\":387222,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387222,\"e\":387222,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387222,\"e\":387222,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387229,\"e\":387229,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387229,\"e\":387229,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387229,\"e\":387229,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387237,\"e\":387237,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387237,\"e\":387237,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387237,\"e\":387237,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387245,\"e\":387245,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387245,\"e\":387245,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387245,\"e\":387245,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387254,\"e\":387254,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387254,\"e\":387254,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387254,\"e\":387254,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387261,\"e\":387261,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387261,\"e\":387261,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387261,\"e\":387261,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387269,\"e\":387269,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387270,\"e\":387270,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":387270,\"e\":387270,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointerout\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":386288,\"e\":386288,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":386966,\"e\":386966,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerout\",\"s\":386966,\"e\":386966,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerout\",\"s\":387277,\"e\":387278,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointerout\",\"s\":387278,\"e\":387278,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"transitionrun\",\"s\":386302,\"e\":386302,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":386852,\"e\":386852,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":389999,\"e\":389999,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":391209,\"e\":391209,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":386502,\"e\":386502,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":386502,\"e\":386502,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":386502,\"e\":386502,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":387185,\"e\":387185,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":387185,\"e\":387185,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":387185,\"e\":387185,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":390249,\"e\":390249,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":390249,\"e\":390249,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":390249,\"e\":390249,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":391209,\"e\":391209,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":391209,\"e\":391209,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":391209,\"e\":391209,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focus\",\"s\":386848,\"e\":386848,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"focus\",\"s\":386848,\"e\":386848,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focus\",\"s\":391194,\"e\":391194,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"focus\",\"s\":391194,\"e\":391194,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusin\",\"s\":386848,\"e\":386848,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusin\",\"s\":386848,\"e\":386848,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusin\",\"s\":391194,\"e\":391194,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusin\",\"s\":391194,\"e\":391194,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerdown\",\"s\":386849,\"e\":386849,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerdown\",\"s\":386849,\"e\":386849,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerdown\",\"s\":386849,\"e\":386849,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":386919,\"e\":386919,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":386919,\"e\":386919,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":386919,\"e\":386919,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":386919,\"e\":386919,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386919,\"e\":386920,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386920,\"e\":386920,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386920,\"e\":386920,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386920,\"e\":386960,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386963,\"e\":386963,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386963,\"e\":386963,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386964,\"e\":386964,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386964,\"e\":386964,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386964,\"e\":386964,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":386964,\"e\":386964,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"transitioncancel\",\"s\":386966,\"e\":386966,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon.andes-spinner__icon--large\"},{\"n\":\"animationstart\",\"s\":386986,\"e\":386986,\"t\":\"event\",\"o\":\"div.andes-spinner__icon.andes-spinner__icon--large\"},{\"n\":\"animationstart\",\"s\":388009,\"e\":388010,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationstart\",\"s\":388010,\"e\":388010,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationiteration\",\"s\":387718,\"e\":387718,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":387718,\"e\":387718,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":387718,\"e\":387718,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":387718,\"e\":387718,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationend\",\"s\":388484,\"e\":388484,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationend\",\"s\":388484,\"e\":388485,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"focusout\",\"s\":389995,\"e\":389995,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusout\",\"s\":389995,\"e\":389995,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusout\",\"s\":391207,\"e\":391207,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusout\",\"s\":391207,\"e\":391207,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"blur\",\"s\":389995,\"e\":389995,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"blur\",\"s\":389995,\"e\":389995,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"blur\",\"s\":391207,\"e\":391207,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"blur\",\"s\":391207,\"e\":391207,\"t\":\"event\",\"o\":\"window\"}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "36"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "text/plain"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:21 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 36, "mimeType": "text/plain", "compression": 0, "text": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}, "redirectURL": "", "headersSize": 328, "bodySize": 36, "_transferSize": 364, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:49.379Z", "time": 182.**************, "timings": {"blocked": 2.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.246, "wait": 179.**************, "receive": 0.****************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=401524&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "907"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "401524"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}], "cookies": [], "headersSize": 938, "bodySize": 907, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":620},\"rxSize\":{\"t\":24},\"duration\":{\"t\":171},\"cbTime\":{\"t\":0},\"time\":{\"t\":391507}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/events/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":114},\"rxSize\":{\"t\":24},\"duration\":{\"t\":173},\"cbTime\":{\"t\":0},\"time\":{\"t\":391511}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/resources/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":27439},\"rxSize\":{\"t\":36},\"duration\":{\"t\":185},\"cbTime\":{\"t\":0},\"time\":{\"t\":392509}}}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:30 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 327, "bodySize": 24, "_transferSize": 351, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:58.395Z", "time": 168.*************, "timings": {"blocked": 2.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.139, "wait": 166.************, "receive": 0.****************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/resources/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=402518&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a&st=*************", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "19744"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "402518"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}, {"name": "st", "value": "*************"}], "cookies": [], "headersSize": 958, "bodySize": 19744, "postData": {"mimeType": "text/plain", "text": "{\"res\":[{\"n\":\"load\",\"s\":392694,\"e\":392694,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":392694,\"e\":392694,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":392694,\"e\":392694,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":401696,\"e\":401696,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":401696,\"e\":401696,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":401696,\"e\":401696,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"Ajax\",\"s\":392509,\"e\":392694,\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":401525,\"e\":401696,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"xmlhttprequest\",\"s\":392510,\"e\":392692,\"o\":\"https://bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":401525,\"e\":401694,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"pointerover\",\"s\":397743,\"e\":397743,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerover\",\"s\":397743,\"e\":397743,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerover\",\"s\":397751,\"e\":397751,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerover\",\"s\":397751,\"e\":397751,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerover\",\"s\":399556,\"e\":399556,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerover\",\"s\":399556,\"e\":399556,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"mousing\",\"s\":397743,\"e\":398342,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"mousing\",\"s\":399557,\"e\":399694,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397744,\"e\":397744,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397744,\"e\":397744,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397744,\"e\":397744,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397751,\"e\":397751,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397751,\"e\":397751,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397751,\"e\":397751,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397759,\"e\":397759,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397759,\"e\":397759,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397759,\"e\":397759,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397767,\"e\":397767,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397767,\"e\":397767,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397767,\"e\":397767,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397774,\"e\":397774,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397774,\"e\":397774,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397775,\"e\":397775,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397783,\"e\":397783,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397783,\"e\":397783,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397783,\"e\":397783,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397918,\"e\":397918,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397918,\"e\":397918,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397918,\"e\":397918,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397933,\"e\":397933,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397934,\"e\":397934,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397934,\"e\":397934,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397950,\"e\":397950,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397950,\"e\":397950,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397950,\"e\":397950,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397960,\"e\":397960,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397960,\"e\":397960,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397960,\"e\":397960,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397965,\"e\":397965,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397966,\"e\":397966,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397966,\"e\":397966,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397974,\"e\":397974,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397974,\"e\":397974,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397974,\"e\":397974,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397985,\"e\":397985,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397985,\"e\":397985,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397985,\"e\":397985,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397991,\"e\":397991,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397991,\"e\":397991,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397991,\"e\":397991,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397999,\"e\":397999,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397999,\"e\":397999,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":397999,\"e\":397999,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398007,\"e\":398007,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398007,\"e\":398007,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398007,\"e\":398007,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398015,\"e\":398015,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398015,\"e\":398015,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398015,\"e\":398015,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398023,\"e\":398023,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398023,\"e\":398023,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398023,\"e\":398023,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398031,\"e\":398031,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398031,\"e\":398031,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398031,\"e\":398031,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398039,\"e\":398039,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398039,\"e\":398039,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398039,\"e\":398039,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398071,\"e\":398071,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398071,\"e\":398071,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398071,\"e\":398071,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398078,\"e\":398079,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398079,\"e\":398079,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398079,\"e\":398079,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398087,\"e\":398087,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398087,\"e\":398087,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398087,\"e\":398087,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398095,\"e\":398095,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398095,\"e\":398095,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398095,\"e\":398095,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398103,\"e\":398103,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398103,\"e\":398103,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398103,\"e\":398103,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398110,\"e\":398110,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398110,\"e\":398110,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398110,\"e\":398110,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398183,\"e\":398183,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398183,\"e\":398183,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":398183,\"e\":398183,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399557,\"e\":399557,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399575,\"e\":399575,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399575,\"e\":399575,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399575,\"e\":399575,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399582,\"e\":399582,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399582,\"e\":399582,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399582,\"e\":399583,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399591,\"e\":399591,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399591,\"e\":399591,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399591,\"e\":399591,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399598,\"e\":399599,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399599,\"e\":399599,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399599,\"e\":399599,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399607,\"e\":399607,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399607,\"e\":399607,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399607,\"e\":399607,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399614,\"e\":399614,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399614,\"e\":399614,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399615,\"e\":399615,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399623,\"e\":399623,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399623,\"e\":399623,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399624,\"e\":399624,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399631,\"e\":399631,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399631,\"e\":399631,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399631,\"e\":399631,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399638,\"e\":399638,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399638,\"e\":399638,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399638,\"e\":399638,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399646,\"e\":399646,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399646,\"e\":399646,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399646,\"e\":399646,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399654,\"e\":399654,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399654,\"e\":399654,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399654,\"e\":399654,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399662,\"e\":399662,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399663,\"e\":399663,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399663,\"e\":399663,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399670,\"e\":399670,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399670,\"e\":399670,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399670,\"e\":399670,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399678,\"e\":399678,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399678,\"e\":399678,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399678,\"e\":399678,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399686,\"e\":399686,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399686,\"e\":399686,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointermove\",\"s\":399686,\"e\":399686,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":397744,\"e\":397744,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":397744,\"e\":397744,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":398342,\"e\":398342,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":398342,\"e\":398342,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":399694,\"e\":399694,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"pointerout\",\"s\":399694,\"e\":399694,\"t\":\"event\",\"o\":\"section.sicf-page-layout.sicf-page-layout--fixed.page-nfe\"},{\"n\":\"animationstart\",\"s\":397990,\"e\":397990,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-hide\"},{\"n\":\"animationstart\",\"s\":397990,\"e\":397990,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-hide\"},{\"n\":\"animationend\",\"s\":398500,\"e\":398500,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-hide\"},{\"n\":\"animationend\",\"s\":398500,\"e\":398500,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-hide\"},{\"n\":\"resize\",\"s\":398796,\"e\":398796,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398826,\"e\":398826,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398851,\"e\":398851,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398874,\"e\":398874,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398890,\"e\":398890,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398910,\"e\":398910,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398927,\"e\":398927,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398955,\"e\":398955,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":398996,\"e\":398996,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":399045,\"e\":399045,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"resize\",\"s\":399065,\"e\":399065,\"t\":\"event\",\"o\":\"window\"}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "36"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "text/plain"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:31 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 36, "mimeType": "text/plain", "compression": 0, "text": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}, "redirectURL": "", "headersSize": 328, "bodySize": 36, "_transferSize": 364, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:27:59.389Z", "time": 186.**************, "timings": {"blocked": 2.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.****************, "wait": 182.*************, "receive": 0.****************, "_blocked_queueing": 1.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=411545&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "610"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "411545"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}], "cookies": [], "headersSize": 938, "bodySize": 610, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":907},\"rxSize\":{\"t\":24},\"duration\":{\"t\":171},\"cbTime\":{\"t\":0},\"time\":{\"t\":401525}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/resources/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":19744},\"rxSize\":{\"t\":36},\"duration\":{\"t\":189},\"cbTime\":{\"t\":0},\"time\":{\"t\":402519}}}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:40 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 327, "bodySize": 24, "_transferSize": 351, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:28:08.415Z", "time": 171.**************, "timings": {"blocked": 1.****************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 168.**************, "receive": 0.****************, "_blocked_queueing": 0.****************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "117", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "111", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "15494", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=421555&ck=0&s=21a204271268b528&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "308"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "421555"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "21a204271268b528"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "1e2b0cf8-0001-b1d4-fc05-018a4bf7de0a"}], "cookies": [], "headersSize": 938, "bodySize": 308, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":610},\"rxSize\":{\"t\":24},\"duration\":{\"t\":173},\"cbTime\":{\"t\":0},\"time\":{\"t\":411545}}}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "content-type", "value": "image/gif"}, {"name": "date", "value": "Thu, 31 Aug 2023 14:27:50 GMT"}, {"name": "x-served-by", "value": "cache-cgh11148-CGH"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 327, "bodySize": 24, "_transferSize": 351, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-08-31T14:28:18.425Z", "time": 169.**************, "timings": {"blocked": 1.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.132, "wait": 167.**************, "receive": 0.****************, "_blocked_queueing": 0.***************}}]}}