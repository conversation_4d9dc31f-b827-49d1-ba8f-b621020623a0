{"nodes": [{"parameters": {}, "id": "e711bc5d-808e-4819-850b-343e0aab6ade", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-3880, 1600]}, {"parameters": {"options": {}}, "id": "700ecd53-86a1-4285-aded-1ac8332e1dae", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-3400, 1600]}, {"parameters": {"jsCode": "// 4-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)\n    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) \n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det['prod']\n        const imposto = det['imposto']\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][\n          'verProc'\n        ].includes('mercadolivre')\n          ? 'CML'\n          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(\n              'Bling'\n            )\n          ? 'D2C'\n          : ''\n        // BC de IPI = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        // Obter valores das novas tags\n        const xPed = produto['xPed'] || '';\n        const infAdProd = det['infAdProd'] || '';\n        const infCpl = xmlData['nfeProc']['NFe']['infNFe']['infAdic']?.['infCpl'] || '';\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',\n          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',\n          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],\n          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          vFrete: vFrete,\n          vOutro: vOutro, \n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],\n          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,\n          vICMS:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],\n          vProd:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],\n          BASEICST: parseFloat(\n            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']\n          ),\n          VLBASEIPI: VLBASEIPI,\n\t\t\t\t\t/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, \n          vIPI:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||\n            0,\n          vIPIDevol:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][\n              'vIPIDevol'\n            ] || 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vPIS: pis ? pis['vPIS'] || 0 : 0,\n          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,\n          cPais: dest['enderDest']['cPais'],\n          xPais: dest['enderDest']['xPais'],\n          CEP: dest['enderDest']['CEP'],\n          ufDest: dest['enderDest']['UF'],\n          xMun: dest['enderDest']['xMun'],\n          xLgr: dest['enderDest']['xLgr'],\n          nro: dest['enderDest']['nro'],\n          xBairro: dest['enderDest']['xBairro'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CODCOB,\n          CST_PIS_COFINS, \n          xPed,\n          infAdProd,\n          infCpl,\n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "29f2d166-a23c-4526-8b98-3cbc9112084f", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3240, 1600]}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "id": "cdf2d0be-9d45-4e5f-a490-1998753db4cc", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3080, 1600]}, {"parameters": {"content": "## Regra Tributaria - finNFe - Finalidade de emissão da NF-e\n\n1 = NF-e normal.\n2 = NF-e complementar.\n3 = NF-e de ajuste.\n4 = Devolução de mercadoria.", "height": 209.7473416040146, "width": 388.85609947320665, "color": 6}, "id": "7978242e-6ac0-4860-8627-e0f2027b8098", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3860, 1360]}, {"parameters": {"content": "## PATH BASE TESTE\n/u01/xml_mimo/mercado_livre/TESTE/*.xml", "height": 199.87149532710242, "width": 350.64252336448556, "color": 4}, "id": "f7846368-43e7-4ec1-acf0-2746764ebecf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3440, 1360]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "venda", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "e0ef98e5-4850-43f4-81b6-bc1a0c7fd004", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2580, 1580]}, {"parameters": {"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## <PERSON>ras Saídas\n• Notas de remessa não devem aparecer na Rotina 111", "height": 306, "width": 530, "color": 7}, "id": "f8b6d694-3609-4b66-b7dc-216d94bf8b37", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1620, 1680]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}, {"id": "b163cce6-a7eb-4d4d-997f-943da7b84c51", "name": "CODUSURDEVOL", "value": 68, "type": "string"}, {"id": "baab24ae-9778-41f8-a3c3-7bcb002bc6cc", "name": "CODFUNC", "value": 1, "type": "number"}, {"id": "67c5933a-e817-4055-baa2-e866fd2189cd", "name": "CODBANCO", "value": 7777, "type": "number"}]}, "includeOtherFields": true, "options": {}}, "id": "1d8b2b22-85de-49b4-90cd-9f2bbf733c05", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-2920, 1600]}, {"parameters": {"content": "## Financeiro\n- Os títulos são baixados em lote pelo financeiro via **Rotina 1207** \n\n- Add NUMCAR = 0 na PCPREST para que os títulos apareçam na rotina 410.\n\n- Add acerto automático dos carregamentos na 410", "width": 554}, "id": "8ed28581-f67a-4f0e-9af2-ab3bdd5385df", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1580, 1260]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1980, 1480], "id": "0487ceac-18ac-415f-b489-5862130768c0", "name": "If"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT C.PROXNUMTRANSVENDA,\n       NVL(<PERSON><PERSON><PERSON>OX<PERSON>,1) PROXNUMTRANS,\n       CLI.CODCLI,\n       C.CODCONTCLI, \n       C.CODCONTFOR,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $('natOp').item.json.CGCFILIAL }}') AS CODFORNEC,\n      (SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $('natOp').item.json.CGCFILIAL }}') AS CODFILIAL\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE APENASNUMEROS(CLI.CGCENT) = '{{ $('Loop').item.json.CGCENT }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1780, 1460], "id": "ebfbb22a-af73-4b70-9918-72355c63fa1d", "name": "PROXNUMTRANSVENDA", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT S.CODCLI, S.NUMTRANSVENDA, S.CHAVENFE \n  FROM PCNFSAID S\n WHERE CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND S.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2160, 1480], "id": "ff1bd16e-83a3-4014-a100-4d353bb69a41", "name": "Validar Nota", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFSAID", "mode": "list", "cachedResultName": "PCNFSAID"}, "fields": {"string": [{"name": "TIPOEMISSAO", "value": "={{ $('Loop').item.json.tpImp }}"}, {"name": "FINALIDADENFE", "value": "={{ $('Loop').item.json.finNFe }}"}, {"name": "CAIXA", "value": "0"}, {"name": "SERIE", "value": "={{ $('Loop').item.json.serie }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json.PROXNUMTRANSVENDA }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop').item.json.nNF }}"}, {"name": "VLFRETE", "value": "={{ $('Loop').item.json.vFrete }}"}, {"name": "VLOUTRASDESP", "value": "={{ $('Loop').item.json.vOutro }}"}, {"name": "TIPOVENDA", "value": "1"}, {"name": "DTSAIDA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTENTREGA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "ICMSRETIDO", "value": "={{ $('Loop').item.json.vST }}"}, {"name": "BCST", "value": "={{ $('Loop').item.json.BASEICST }}"}, {"name": "VLIPI", "value": "={{ $('Loop').item.json.vIPI }}"}, {"name": "VLPIS", "value": "={{ $('Loop').item.json.vPIS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop').item.json.vCOFINS }}"}, {"name": "CODPAIS", "value": "={{ $('Loop').item.json.cPais }}"}, {"name": "DESCPAIS", "value": "={{ $('Loop').item.json.xPais }}"}, {"name": "CEP", "value": "={{ $('Loop').item.json.CEP }}"}, {"name": "UF", "value": "={{ $('Loop').item.json.ufDest }}"}, {"name": "MUNICIPIO", "value": "={{ $('Loop').item.json.xMun }}"}, {"name": "ENDERECO", "value": "={{ $('Loop').item.json.xLgr }}"}, {"name": "NUMENDERECO", "value": "={{ $('Loop').item.json.nro }}"}, {"name": "BAIRRO", "value": "={{ $('Loop').item.json.xBairro }}"}, {"name": "VLTOTGER", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "CHAVENFE", "value": "={{ $('Loop').item.json.chNFe }}"}, {"name": "PRAZOPONDERADO", "value": "N"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "GERACP", "value": "N"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "EMISSNUMAUTOMATICO", "value": "N"}, {"name": "CTEREGIMEESPECIAL", "value": "N"}, {"name": "NFIPIEMITIDA", "value": "N"}, {"name": "REDUZICMSDOCTE", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $('Loop').item.json.CST_PIS_COFINS }}"}, {"name": "CODFISCALNF", "value": "={{ $('Loop').item.json.CFOP }}"}, {"name": "DTLANCTO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop').item.json.VLBASEIPI }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "VLBASEPISCOFINS", "value": "0"}, {"name": "CODCOB", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "CONDVENDA", "value": "1"}, {"name": "SITUACAONFE", "value": "={{ $('Loop').item.json.cStat }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json.CODCLI }}"}, {"name": "CODCLINF", "value": "={{ $json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "PROTOCOLONFE", "value": "={{ $('Loop').item.json.nProt }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop').item.json.ufDest === $('Loop').item.json.ufEmit ? 599 : 699 }}"}, {"name": "CODCONT", "value": "={{ $json.CODCONTCLI }}"}, {"name": "CODFORNEC", "value": "={{ $json.CODFORNEC }}"}, {"name": "CODUSUR", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "NUMVIAS", "value": "1"}, {"name": "CODPRACA", "value": "={{ $('Loop').item.json.CODPRACA }}"}, {"name": "NUMPED"}, {"name": "NUMPEDCLI", "value": "={{ $('Loop').item.json.xPed }}"}, {"name": "OBS", "value": "={{ $('Loop').item.json.infAdProd }}"}, {"name": "OBSNFE", "value": "={{ $('Loop').item.json.infCpl }}"}, {"name": "DTFECHA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1600, 1460], "id": "e9038446-e338-4849-a79a-df66e52af5d9", "name": "PCNFSAID", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCPRESTOBS", "mode": "list", "cachedResultName": "PCPRESTOBS"}, "fields": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVEND<PERSON>').item.json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "PREST", "value": "1"}, {"name": "PENDENCIA", "value": "N"}, {"name": "OBSGERAIS", "value": "={{ $('natOp').item.json.xPed }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1060, 1460], "id": "2f694087-4591-4fff-baff-b1decd15fcaa", "name": "PCPRESTOBS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMTRANSVENDA = NVL(PROXNUMTRANSVENDA,0) + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-700, 1460], "id": "60739270-e0ed-4513-ab43-db4122973db7", "name": "Atualizar a sequencia", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1980, 1740], "id": "2ea6aba8-5364-46d2-9408-6c064d35d39a", "name": "If1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT C.PROXNUMTRANSVENDA,\n       CLI.CODCLI,\n       C.CODCONTCLI, \n       C.CODCONTFOR,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $('natOp').item.json.CGCFILIAL }}') AS CODFORNEC,\n      (SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $('natOp').item.json.CGCFILIAL }}') AS CODFILIAL\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE 1 = 1\n    --AND CODUSUR1 = {{ $json[\"previousData\"][\"CODRCA\"] }}\n    AND APENASNUMEROS(CGCENT) = '{{ $('natOp').item.json.CGCENT }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1780, 1720], "id": "e566e4b8-d3d1-4eab-8d03-44a0781ea56a", "name": "PROXNUMTRANSVENDA1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT S.CODCLI, S.NUMTRANSVENDA, S.CHAVENFE \n  FROM PCNFSAID S\n WHERE CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND S.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2160, 1740], "id": "d5e6e585-355b-42c9-b08e-f10a5202801d", "name": "Validar Nota1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFSAID", "mode": "list", "cachedResultName": "PCNFSAID"}, "fields": {"string": [{"name": "TIPOEMISSAO", "value": "={{ $('Loop1').item.json.tpImp }}"}, {"name": "FINALIDADENFE", "value": "={{ $('Loop1').item.json.finNFe }}"}, {"name": "CAIXA", "value": "0"}, {"name": "SERIE", "value": "={{ $('Loop1').item.json.serie }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json.PROXNUMTRANSVENDA }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop1').item.json.nNF }}"}, {"name": "VLFRETE", "value": "={{ $('Loop1').item.json.vFrete }}"}, {"name": "VLOUTRASDESP", "value": "={{ $('Loop1').item.json.vOutro }}"}, {"name": "TIPOVENDA", "value": "SR"}, {"name": "DTSAIDA", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTENTREGA", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $('Loop1').item.json.vNF }}"}, {"name": "ICMSRETIDO", "value": "={{ $('Loop1').item.json.vST }}"}, {"name": "BCST", "value": "={{ $('Loop1').item.json.BASEICST }}"}, {"name": "VLIPI", "value": "={{ $('Loop1').item.json.vIPI }}"}, {"name": "VLPIS", "value": "={{ $('Loop1').item.json.vPIS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop1').item.json.vCOFINS }}"}, {"name": "CODPAIS", "value": "={{ $('Loop1').item.json.cPais }}"}, {"name": "DESCPAIS", "value": "={{ $('Loop1').item.json.xPais }}"}, {"name": "CEP", "value": "={{ $('Loop1').item.json.CEP }}"}, {"name": "UF", "value": "={{ $('Loop1').item.json.ufDest }}"}, {"name": "MUNICIPIO", "value": "={{ $('Loop1').item.json.xMun }}"}, {"name": "ENDERECO", "value": "={{ $('Loop1').item.json.xLgr }}"}, {"name": "NUMENDERECO", "value": "={{ $('Loop1').item.json.nro }}"}, {"name": "BAIRRO", "value": "={{ $('Loop1').item.json.xBairro }}"}, {"name": "VLTOTGER", "value": "={{ $('Loop1').item.json.vNF }}"}, {"name": "CHAVENFE", "value": "={{ $('Loop1').item.json.chNFe }}"}, {"name": "PRAZOPONDERADO", "value": "N"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "GERACP", "value": "N"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "EMISSNUMAUTOMATICO", "value": "N"}, {"name": "CTEREGIMEESPECIAL", "value": "N"}, {"name": "NFIPIEMITIDA", "value": "N"}, {"name": "REDUZICMSDOCTE", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $('Loop1').item.json.CST_PIS_COFINS }}"}, {"name": "CODFISCALNF", "value": "={{ $('Loop1').item.json.CFOP }}"}, {"name": "DTLANCTO", "value": "={{ $('Loop1').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop1').item.json.VLBASEIPI }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "VLBASEPISCOFINS", "value": "0"}, {"name": "CODCOB", "value": "={{ $('Loop1').item.json.CODCOB }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "CONDVENDA", "value": "1"}, {"name": "SITUACAONFE", "value": "={{ $('Loop1').item.json.cStat }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json.CODCLI }}"}, {"name": "CODCLINF", "value": "={{ $json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "PROTOCOLONFE", "value": "={{ $('Loop1').item.json.nProt }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop1').item.json.ufDest === $('Loop1').item.json.ufEmit ? 599 : 699 }}"}, {"name": "CODCONT", "value": "={{ $json.CODCONTCLI }}"}, {"name": "CODFORNEC", "value": "={{ $json.CODFORNEC }}"}, {"name": "CODUSUR", "value": "={{ $('Loop1').item.json.CODRCA }}"}, {"name": "NUMVIAS", "value": "1"}, {"name": "CODPRACA", "value": "={{ $('Loop1').item.json.CODPRACA }}"}, {"name": "OBS", "value": "={{ $('Loop1').item.json.infAdProd }}"}, {"name": "OBSNFE", "value": "={{ $('Loop1').item.json.infCpl }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1600, 1720], "id": "02672b3a-e080-4d14-9b16-ba288d073aab", "name": "PCNFSAID1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2360, 1460], "id": "e8aa052a-c2dc-42e2-b05a-9690f48d94fb", "name": "Loop"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2360, 1720], "id": "b497ac55-fa6e-46ac-b5a4-39de2b1ec3cc", "name": "Loop1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMTRANSVENDA = PROXNUMTRANSVENDA + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1240, 1720], "id": "147d08ae-e2c6-4e73-9701-f6b66962a113", "name": "Atualizar a sequencia1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "list", "cachedResultName": "PCNFBASE"}, "fields": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVENDA1').item.json.PROXNUMTRANSVENDA }}"}, {"name": "NUMTRANSENT"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('Loop1').item.json.CFOP }}"}, {"name": "VLBASE", "value": "={{ $('Loop1').item.json.BASEICMS }}"}, {"name": "VLICMS", "value": "={{ $('Loop1').item.json.vICMS }}"}, {"name": "ALIQUOTA", "value": "={{ $('Loop1').item.json.pICMS }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1420, 1720], "id": "1bd911c9-c3b8-43f0-8daa-503396ef89af", "name": "PCNFBASE1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "list", "cachedResultName": "PCNFBASE"}, "fields": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVEND<PERSON>').item.json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "NUMTRANSENT"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('Loop').item.json.CFOP }}"}, {"name": "VLBASE", "value": "={{ $('Loop').item.json.BASEICMS }}"}, {"name": "VLICMS", "value": "={{ $('Loop').item.json.vICMS }}"}, {"name": "ALIQUOTA", "value": "={{ $('Loop').item.json.pICMS }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-880, 1460], "id": "2dcaa962-b4d0-4e0b-8e40-2222a1562b23", "name": "PCNFBASE", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "3433e46f-d719-414b-96a6-ede33dcec26d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2760, 1600]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCPREST", "mode": "list", "cachedResultName": "PCPREST"}, "fields": {"string": [{"name": "CODCLI", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODFILIAL }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVENDA').item.json.PROXNUMTRANSVENDA }}"}, {"name": "VALOR", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "PREST", "value": "1"}, {"name": "DUPLIC", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "DTVENC", "value": "={{ new Date(new Date($now).getTime() + 45 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR') }}"}, {"name": "DTVENCORIG", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTEMISSAO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODCOB", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "CODCOBORIG", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "STATUS", "value": "A"}, {"name": "VALORORIG", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "TXPERM", "value": "0"}, {"name": "OPERACAO", "value": "S"}, {"name": "CODUSUR", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODFUNCCXMOT", "value": "={{ $('Loop').item.json.CODFUNC }}"}, {"name": "DTTRANSACAOCC", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTCXMOT", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTCXMOTHHMMSS", "value": "={{  $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" +  $('Loop').item.json.dhEmi.split('T')[1].split('-')[0] }}"}, {"name": "DTULTALTER", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFUNCULTALTER", "value": "={{ $('Loop').item.json.CODFUNC }}"}, {"name": "TIPOOPERACAOTEF", "value": "C"}, {"name": "DTFECHA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "HORAFECHA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[1].split(':')[0] }}"}, {"name": "MINUTOFECHA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[1].split(':')[1] }}"}, {"name": "VPAGO", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "DTPAG", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTBAIXA", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODBAIXA", "value": "={{ $('Loop').item.json.CODFUNC }}"}, {"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANSVENDA').item.json.PROXNUMTRANS }}"}, {"name": "CODBANCO", "value": "={{ $('Loop').item.json.CODBANCO }}"}, {"name": "CODCOBBANCO", "value": "D"}, {"name": "CODFUNCFECHA", "value": "={{ $('Loop').item.json.CODFUNC }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1420, 1460], "id": "4595861b-9ecf-4881-a15d-3ac982213c24", "name": "PCPREST", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO PC<PERSON><PERSON><PERSON><PERSON>NTOMAP(                       \n            NUMTRANSVENDA                          \n          , CODCLI                                 \n          , PREST                                  \n          , DUPLIC                                 \n          , NUMCAR                                 \n          , NUMCHECKOUT                            \n          , CODFUNCCHECKOUT                        \n          , VALOR                                  \n          , DTVENC                                 \n          , CODCOB                                 \n          , VPAGO                                  \n          , TXPERM                                 \n          , DTPAG                                  \n          , DTEMISSAO                              \n          , PERDESC                                \n          , CODFILIAL                              \n          , DTVENCORIG                             \n          , CODCOBORIG                             \n          , NSUTEF                                 \n          , PRESTTEF                               \n          , QTPARCELASPOS                          \n          , OBSERVACAOMAP                          \n          , DATA                                   \n          , GERAPA<PERSON><PERSON>AMENTOTEF                    \n          , INFORMADADOSBXCCRED                    \n          , DESDCARTAOFECHCAR<PERSON>                    \n          , USOUPARCELAMENTOAUTOMATICO             \n          , USOUPARCELAMENTOMANUAL                 \n          , TITCOMNUMCARCAIXA                      \n          , PERMITEVENDAECF402                     \n          , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>MENT<PERSON>                         \n          , NUMSEQ                                 \n          )                                        \n      SELECT                                       \n            PCPREST.NUMTRANSVENDA                  \n          , PCPREST.CODCLI                         \n          , PCPREST.PREST                          \n          , PCPREST.DUPLIC                         \n          , PCPREST.NUMCAR                         \n          , PCPREST.NUMCHECKOUT                    \n          , PCPREST.CODFUNCCHECKOUT                \n          , PCPREST.VALOR                          \n          , PCPREST.DTVENC                         \n          , PCPREST.CODCOB                         \n          , PCPREST.VPAGO                          \n          , PCPREST.TXPERM                         \n          , PCPREST.DTPAG                          \n          , PCPREST.DTEMISSAO                      \n          , PCPREST.PERDESC                        \n          , PCPREST.CODFILIAL                      \n          , PCPREST.DTVENCORIG                     \n          , PCPREST.CODCOBORIG                     \n          , PCPREST.NSUTEF                         \n          , PCPREST.PRESTTEF                       \n          , PCPREST.QTPARCELASPOS                  \n          , 'AcertarOuCancelarTitulo'                         \n          , SYSDATE                                \n          , 'S'                   \n          , 'N'                   \n          , 'N'                   \n          , 'N'            \n          , 'N'                \n          , 'N'                     \n          , 'N'                    \n          , 'Acerto de Carregamento'                        \n          , DFSEQ_PCFECHAMENTOMAP_NUMSEQ.NEXTVAL                        \n       FROM PCPREST                                \n      WHERE PCPREST.NUMTRANSVENDA = {{ $('PROXNUMTRANSVENDA').item.json.PROXNUMTRANSVENDA }}\n        AND PCPREST.PREST = '1' ", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1240, 1460], "id": "275f5df4-391e-40d4-aab0-97effabdf022", "name": "PCFECHAMENTOMAP", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE \n   vContador         NUMBER := 0; \n   vSaldo            NUMBER := 0; \n   vSaldoCredito     NUMBER := 0; \n   vSaldoDebito      NUMBER := 0; \n   vSaldoInicial     NUMBER := 0;\n   vCodBanco         NUMBER := {{ $json.CODBANCO }};\n   vCodCob           VARCHAR2(1) := 'D';\n   vDtUltCompensacao DATE;\n   vAno              VARCHAR2(4);\n   vMes              VARCHAR2(2);\nBEGIN \n   -- Extrai ano e mês da variável dhEmi\n   vAno := '{{ $json.dhEmi.split('T')[0].split('-')[0]}}';\n   vMes := '{{ $json.dhEmi.split('T')[0].split('-')[1]}}';\n\n   SELECT MAX(TRUNC(DTULTCOMPENSACAO)) \n     INTO vDtUltCompensacao \n     FROM PCESTCR;\n\n   SELECT NVL(SALDO,0) \n     INTO vSaldoInicial \n     FROM PCESTCRCOMPENSACAO \n    WHERE CODBANCO = vCodBanco \n      AND CODMOEDA = vCodCob \n      AND ANO = vAno \n      AND MES = vMes \n      AND ROWNUM = 1 \n    ORDER BY ANO, MES;\n\n   FOR REG IN (SELECT TIPO, \n                      VALOR, \n                      ROWID ID \n               FROM PCMOVCR \n               WHERE CODBANCO = vCodBanco\n                 AND CODCOB = vCodCob\n  ) \n   LOOP \n      IF LENGTH(REG.ID) > 0 THEN\n         UPDATE PCMOVCR\n            SET VLSALDOCOMP = vSaldoInicial + vSaldoDebito - vSaldoCredito\n          WHERE ROWID = REG.ID; \n      END IF; \n   END LOOP;\n\n   SELECT SUM(DECODE(TIPO, 'D', NVL(VALOR, 0), 0)) TOTALDEBITOS,\n          SUM(DECODE(TIPO, 'C', NVL(VALOR, 0), 0)) TOTALCREDITOS\n     INTO vSaldoDebito,\n          vSaldoCredito\n     FROM PCMOVCR\n    WHERE CODBANCO = vCodBanco\n      AND CODCOB = vCodCob\n      AND ((OPERACAO <> 99) OR (OPERACAO IS NULL))\n      AND DTCOMPENSACAO IS NOT NULL; \n   \n   vSaldo := vSaldoDebito - vSaldoCredito; \n   vContador := 0;\n\n   SELECT COUNT(1)\n     INTO vContador\n     FROM PCESTCR\n    WHERE CODCOB = vCodCob\n      AND CODBANCO = vCodBanco; \n   \n   IF vContador = 0 THEN \n      INSERT INTO PCESTCR \n         (CODCOB,   \n          CODBANCO, \n          VALOR,    \n          VALORSALDOTOTALCONCIL, \n          VALORSALDOTOTALCOMP, \n          VALORCONCILIADO, \n          VALORCOMPENSADO) \n      VALUES \n         (vCodCob, \n          vCodBanco,\n          0, \n          0, \n          0, \n          0, \n          0); \n   END IF; \n   \n   UPDATE PCESTCR \n      SET VALORSALDOTOTALCOMP = vSaldo, \n          VALORCOMPENSADO = vSaldo, \n          DTULTCOMPENSACAO = vDtUltCompensacao\n    WHERE CODBANCO = vCodBanco\n      AND CODCOB = vCodCob; \n\n   -- Atualiza a tabela PCESTCRCOMPENSACAO ou insere novos dados se não existirem registros\n   SELECT COUNT(1)\n     INTO vContador\n     FROM PCESTCRCOMPENSACAO\n    WHERE CODBANCO = vCodBanco \n      AND CODMOEDA = vCodCob \n      AND MES = vMes           \n      AND ANO = vAno;\n\n   IF vContador > 0 THEN\n      UPDATE PCESTCRCOMPENSACAO \n         SET VALORCREDITO     = vSaldoCredito, \n             VALORDEBITO      = vSaldoDebito,  \n             SALDOINICIAL     = vSaldoInicial, \n             SALDO            = vSaldo,         \n             DATASALDOINICIAL = vDtUltCompensacao\n       WHERE CODBANCO = vCodBanco \n         AND CODMOEDA = vCodCob \n         AND MES = vMes           \n         AND ANO = vAno;\n   ELSE\n      INSERT INTO PCESTCRCOMPENSACAO (MES, ANO, CODBANCO, CODMOEDA, VALORDEBITO, VALORCREDITO, DATASALDOINICIAL, SALDO, SALDOINICIAL)\n      VALUES (vMes, vAno, vCodBanco, vCodCob, vSaldoDebito, vSaldoCredito, vDtUltCompensacao, vSaldo, vSaldoInicial);\n   END IF;\n\n   -- Reset das variáveis\n   vSaldo := NULL; \n   vSaldoCredito := NULL; \n   vSaldoDebito := NULL; \n   vSaldoInicial := NULL;\nEND;\n", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2020, 1160], "id": "a2ccf950-de5b-4db1-a231-9e60468c0ad6", "name": "<PERSON><PERSON><PERSON>", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "disabled": true}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCPREST", "mode": "list", "cachedResultName": "PCPREST"}, "fields": {"string": [{"name": "CODCLI", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $('PROXNUMTRANSVENDA').item.json.CODFILIAL }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVENDA').item.json.PROXNUMTRANSVENDA }}"}, {"name": "VALOR", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "PREST", "value": "1"}, {"name": "DUPLIC", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "DTVENC", "value": "={{ new Date(new Date($now).getTime() + 45 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR') }}"}, {"name": "DTVENCORIG", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTEMISSAO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODCOB", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "CODCOBORIG", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "STATUS", "value": "A"}, {"name": "VALORORIG", "value": "={{ $('Loop').item.json.vNF }}"}, {"name": "TXPERM", "value": "0"}, {"name": "OPERACAO", "value": "S"}, {"name": "CODUSUR", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "NUMCAR", "value": "0"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1740, 1160], "id": "f0692974-74fa-42c7-88eb-6603a62214a6", "name": "PCPREST1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}, "disabled": true}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-3720, 1600], "id": "47ad14f7-112a-4f1a-bc9a-d0d94366372f", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-3560, 1600], "id": "c59ba940-1c98-4ba1-b35e-34b0fe408e48", "name": "Extract from File"}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "PROXNUMTRANSVENDA", "type": "main", "index": 0}], [{"node": "Loop", "type": "main", "index": 0}]]}, "PROXNUMTRANSVENDA": {"main": [[{"node": "PCNFSAID", "type": "main", "index": 0}]]}, "Validar Nota": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "PCNFSAID": {"main": [[{"node": "PCPREST", "type": "main", "index": 0}]]}, "PCPRESTOBS": {"main": [[{"node": "PCNFBASE", "type": "main", "index": 0}]]}, "Atualizar a sequencia": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "PROXNUMTRANSVENDA1", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "PROXNUMTRANSVENDA1": {"main": [[{"node": "PCNFSAID1", "type": "main", "index": 0}]]}, "Validar Nota1": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "PCNFSAID1": {"main": [[{"node": "PCNFBASE1", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "Validar Nota", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Validar Nota1", "type": "main", "index": 0}]]}, "Atualizar a sequencia1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "PCNFBASE1": {"main": [[{"node": "Atualizar a sequencia1", "type": "main", "index": 0}]]}, "PCNFBASE": {"main": [[{"node": "Atualizar a sequencia", "type": "main", "index": 0}]]}, "Saida": {"main": [[{"node": "natOp", "type": "main", "index": 0}]]}, "PCPREST": {"main": [[{"node": "PCFECHAMENTOMAP", "type": "main", "index": 0}]]}, "PCFECHAMENTOMAP": {"main": [[{"node": "PCPRESTOBS", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}