{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 941457}, {"functionName": "e.exports", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 939785}, {"functionName": "e.exports", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 946012}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "l.request", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 944042}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 949240}, {"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 593112}, {"functionName": "t.doRequest", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 360018}, {"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 593283}, {"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 458018}, {"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420260}, {"functionName": "onClick", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "request": {"method": "GET", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/api/nfe/zip/validate?start=********&end=********&sale=all&return=all&full=all&others=all&file_types=xml&simple_folder=false", "httpVersion": "h3", "headers": [{"name": ":authority", "value": "myaccount.mercadolivre.com.br"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/invoices/documents/api/nfe/zip/validate?start=********&end=********&sale=all&return=all&full=all&others=all&file_types=xml&simple_folder=false"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "application/json, text/plain, */*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "cookie", "value": "_hjSessionUser_580848=********************************************************************************************************************; cookiesPreferencesNotLogged=%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%2C%22performance%22%3Atrue%2C%22functionality%22%3Atrue%7D%7D; _ml_ga=GA1.3.*********.**********; _ml_ci=*********.**********; _d2id=fc6aa148-edc3-4dec-a790-2b7aebcf2408; orgnickp=MIMO_SA; ssid=ghy-090113-vAlzBmOj4MOxvPPAWkWJ9RrzSTw0mk-__-*********-__-1788282985880--RRR_0-RRR_0; ftid=d0qvaWRAdL0jjjkNsAKEoVDmbX7EKgMw-1693588135653; orguserid=Zh47904Th4ZZ; orguseridp=*********; cookiesPreferencesLoggedFallback=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; cp=********; _hjSessionUser_720738=eyJpZCI6IjE1MGZlMjkyLWJhY2EtNWM4MC1iYjJkLWFhY2M0MzQ2NWYzYiIsImNyZWF0ZWQiOjE2OTM1ODgxNjA2NTgsImV4aXN0aW5nIjp0cnVlfQ==; _gcl_au=1.1.1845951909.1693588653; _mshops_ga=GA1.3.326949057.1693588668; _gid=GA1.3.790250885.1693826240; _hjSession_580848=eyJpZCI6ImZhNzFlYWI2LTAwOTEtNDcwNy1iMzM0LTBjZmQ2MWYwMDIxOSIsImNyZWF0ZWQiOjE2OTM4MzQ2MDQzODgsImluU2FtcGxlIjp0cnVlfQ==; _hjAbsoluteSessionInProgress=0; _mldataSessionId=5a012e2c-f590-42b7-8230-463254702ca7; _ml_ga_gid=GA1.3.938323594.1693837590; _hjIncludedInSessionSample_580848=1; _hjSession_720738=********************************************************************************************************************; _csrf=OgDIYUDrLsCCDqgY32XGFCa8; _ga=GA1.1.*********.**********; _uetsid=2af819a04b3711eeb9dfed55c48f6324; _uetvid=705460a048eb11eea11f8dc2e024e981; _ga_NDJFKMJ2PD=GS1.1.**********.2.1.**********.49.0.0; _hjShownFeedbackMessage=true"}, {"name": "device-memory", "value": "8"}, {"name": "downlink", "value": "10"}, {"name": "dpr", "value": "1.25"}, {"name": "ect", "value": "4g"}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "rtt", "value": "50"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}, {"name": "viewport-width", "value": "1536"}, {"name": "x-csrf-token", "value": "wMxlcVBc-4Hz_tKC6h-hQuGJCRHcZ0RJ5V-A"}, {"name": "x-flow-starter", "value": "true"}, {"name": "x-newrelic-id", "value": "XQ4OVF5VGwEBVllbAgcFUw=="}, {"name": "x-request-id", "value": "3954031d-477a-4a24-9aa7-cface2e6d4c8"}], "queryString": [{"name": "start", "value": "********"}, {"name": "end", "value": "********"}, {"name": "sale", "value": "all"}, {"name": "return", "value": "all"}, {"name": "full", "value": "all"}, {"name": "others", "value": "all"}, {"name": "file_types", "value": "xml"}, {"name": "simple_folder", "value": "false"}], "cookies": [{"name": "_hjSessionUser_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T15:27:00.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesNotLogged", "value": "%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%2C%22performance%22%3Atrue%2C%22functionality%22%3Atrue%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T02:20:19.319Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ml_ga", "value": "GA1.3.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:27:00.316Z", "httpOnly": false, "secure": false}, {"name": "_ml_ci", "value": "*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_d2id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:09:19.702Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orgnickp", "value": "MIMO_SA", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ssid", "value": "ghy-090113-vAlzBmOj4MOxvPPAWkWJ9RrzSTw0mk-__-*********-__-1788282985880--RRR_0-RRR_0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "ftid", "value": "d0qvaWRAdL0jjjkNsAKEoVDmbX7EKgMw-1693588135653", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "orguserid", "value": "Zh47904Th4ZZ", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orguseridp", "value": "*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged<PERSON><PERSON>back", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-11T15:24:26.757Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cp", "value": "********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-10-01T17:16:54.935Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_hjSessionUser_720738", "value": "eyJpZCI6IjE1MGZlMjkyLWJhY2EtNWM4MC1iYjJkLWFhY2M0MzQ2NWYzYiIsImNyZWF0ZWQiOjE2OTM1ODgxNjA2NTgsImV4aXN0aW5nIjp0cnVlfQ==", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T15:24:28.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_gcl_au", "value": "1.1.1845951909.1693588653", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-30T17:17:32.000Z", "httpOnly": false, "secure": false}, {"name": "_mshops_ga", "value": "GA1.3.326949057.1693588668", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:17:48.224Z", "httpOnly": false, "secure": false}, {"name": "_gid", "value": "GA1.3.790250885.1693826240", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:15:39.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSession_580848", "value": "eyJpZCI6ImZhNzFlYWI2LTAwOTEtNDcwNy1iMzM0LTBjZmQ2MWYwMDIxOSIsImNyZWF0ZWQiOjE2OTM4MzQ2MDQzODgsImluU2FtcGxlIjp0cnVlfQ==", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T16:00:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjAbsoluteSessionInProgress", "value": "0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T16:00:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_mldataSessionId", "value": "5a012e2c-f590-42b7-8230-463254702ca7", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:57:00.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ml_ga_gid", "value": "GA1.3.938323594.1693837590", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:27:00.000Z", "httpOnly": false, "secure": false}, {"name": "_hjIncludedInSessionSample_580848", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:31:57.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_720738", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:54:28.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_csrf", "value": "OgDIYUDrLsCCDqgY32XGFCa8", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true}, {"name": "_ga", "value": "GA1.1.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:24:41.077Z", "httpOnly": false, "secure": false}, {"name": "_uetsid", "value": "2af819a04b3711eeb9dfed55c48f6324", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:24:41.000Z", "httpOnly": false, "secure": false}, {"name": "_uetvid", "value": "705460a048eb11eea11f8dc2e024e981", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-28T15:24:41.000Z", "httpOnly": false, "secure": false}, {"name": "_ga_NDJFKMJ2PD", "value": "GS1.1.**********.2.1.**********.49.0.0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:24:52.769Z", "httpOnly": false, "secure": false}, {"name": "_hjShownFeedbackMessage", "value": "true", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "2023-09-05T15:25:51.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "h3", "headers": [{"name": "accept-ch", "value": "device-memory, dpr, viewport-width, rtt, downlink, ect, save-data"}, {"name": "accept-ch-lifetime", "value": "60"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "content-length", "value": "15"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Mon, 04 Sep 2023 15:29:39 GMT"}, {"name": "etag", "value": "W/\"f-M44wpu/0uQc80d9aNEVqZ9p98lY\""}, {"name": "expect-ct", "value": "max-age=0"}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "set-cookie", "value": "cookiesPreferencesLogged=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; Max-Age=300; Domain=mercadolivre.com.br; Path=/; Expires=Mon, 04 Sep 2023 15:34:38 GMT; Secure; SameSite=None"}, {"name": "set-cookie", "value": "cookiesPreferencesLoggedFallback=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; Max-Age=604800; Domain=mercadolivre.com.br; Path=/; Expires=Mon, 11 Sep 2023 15:29:38 GMT; Secure; SameSite=None"}, {"name": "strict-transport-security", "value": "max-age=15552000; includeSubDomains"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "1.1 88ed6867c0889ee2caec1c3973510680.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "TCVvrQJBO3iCwxy-l9bVKxKPBJEr948LHQvIku82oZdidrsH5YRL8g=="}, {"name": "x-amz-cf-pop", "value": "GRU3-C1"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-d2id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408"}, {"name": "x-dns-prefetch-control", "value": "on"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-envoy-upstream-service-time", "value": "654"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-request-device-id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408"}, {"name": "x-request-id", "value": "fb817c90-f2ca-4ef4-9749-dcbec0dde8e7"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [{"name": "cookiesPreferencesLogged", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": "mercadolivre.com.br", "expires": "2023-09-04T15:35:05.826Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged<PERSON><PERSON>back", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": "mercadolivre.com.br", "expires": "2023-09-11T15:30:05.826Z", "httpOnly": false, "secure": true, "sameSite": "None"}], "content": {"size": 15, "mimeType": "application/json", "text": "{\"empty\":false}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 1103, "_error": null}, "serverIPAddress": "***********", "startedDateTime": "2023-09-04T15:30:05.823Z", "time": 832.9300000002893, "timings": {"blocked": 3.387999999575084, "dns": 13.385, "ssl": 9.099, "connect": 22.745, "send": 0.7399999999999984, "wait": 791.6539999998738, "receive": 1.0180000008404022, "_blocked_queueing": 2.824999999575084}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 421558}, {"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 421464}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420738}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420275}, {"functionName": "onClick", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}, "_priority": "VeryHigh", "_resourceType": "document", "cache": {}, "request": {"method": "GET", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe/download?start=********&end=********&sale=all&return=all&full=all&others=all&file_types=xml&simple_folder=false", "httpVersion": "h3", "headers": [{"name": ":authority", "value": "myaccount.mercadolivre.com.br"}, {"name": ":method", "value": "GET"}, {"name": ":path", "value": "/invoices/documents/nfe/download?start=********&end=********&sale=all&return=all&full=all&others=all&file_types=xml&simple_folder=false"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "cookie", "value": "_hjSessionUser_580848=********************************************************************************************************************; cookiesPreferencesNotLogged=%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%2C%22performance%22%3Atrue%2C%22functionality%22%3Atrue%7D%7D; _ml_ga=GA1.3.*********.**********; _ml_ci=*********.**********; _d2id=fc6aa148-edc3-4dec-a790-2b7aebcf2408; orgnickp=MIMO_SA; ssid=ghy-090113-vAlzBmOj4MOxvPPAWkWJ9RrzSTw0mk-__-*********-__-1788282985880--RRR_0-RRR_0; ftid=d0qvaWRAdL0jjjkNsAKEoVDmbX7EKgMw-1693588135653; orguserid=Zh47904Th4ZZ; orguseridp=*********; cookiesPreferencesLoggedFallback=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D; cp=********; _hjSessionUser_720738=eyJpZCI6IjE1MGZlMjkyLWJhY2EtNWM4MC1iYjJkLWFhY2M0MzQ2NWYzYiIsImNyZWF0ZWQiOjE2OTM1ODgxNjA2NTgsImV4aXN0aW5nIjp0cnVlfQ==; _gcl_au=1.1.1845951909.1693588653; _mshops_ga=GA1.3.326949057.1693588668; _gid=GA1.3.790250885.1693826240; _hjSession_580848=eyJpZCI6ImZhNzFlYWI2LTAwOTEtNDcwNy1iMzM0LTBjZmQ2MWYwMDIxOSIsImNyZWF0ZWQiOjE2OTM4MzQ2MDQzODgsImluU2FtcGxlIjp0cnVlfQ==; _hjAbsoluteSessionInProgress=0; _mldataSessionId=5a012e2c-f590-42b7-8230-463254702ca7; _ml_ga_gid=GA1.3.938323594.1693837590; _hjIncludedInSessionSample_580848=1; _hjSession_720738=********************************************************************************************************************; _csrf=OgDIYUDrLsCCDqgY32XGFCa8; _ga=GA1.1.*********.**********; _uetsid=2af819a04b3711eeb9dfed55c48f6324; _uetvid=705460a048eb11eea11f8dc2e024e981; _ga_NDJFKMJ2PD=GS1.1.**********.2.1.**********.49.0.0; _hjShownFeedbackMessage=true; cookiesPreferencesLogged=%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D"}, {"name": "device-memory", "value": "8"}, {"name": "downlink", "value": "10"}, {"name": "dpr", "value": "1.25"}, {"name": "ect", "value": "4g"}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "rtt", "value": "50"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "document"}, {"name": "sec-fetch-mode", "value": "navigate"}, {"name": "sec-fetch-site", "value": "same-origin"}, {"name": "sec-fetch-user", "value": "?1"}, {"name": "upgrade-insecure-requests", "value": "1"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}, {"name": "viewport-width", "value": "1536"}], "queryString": [{"name": "start", "value": "********"}, {"name": "end", "value": "********"}, {"name": "sale", "value": "all"}, {"name": "return", "value": "all"}, {"name": "full", "value": "all"}, {"name": "others", "value": "all"}, {"name": "file_types", "value": "xml"}, {"name": "simple_folder", "value": "false"}], "cookies": [{"name": "_hjSessionUser_580848", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T15:27:00.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesNotLogged", "value": "%7B%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22traceability%22%3Atrue%2C%22performance%22%3Atrue%2C%22functionality%22%3Atrue%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-08-30T02:20:19.319Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ml_ga", "value": "GA1.3.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:27:00.316Z", "httpOnly": false, "secure": false}, {"name": "_ml_ci", "value": "*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_d2id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:09:19.702Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orgnickp", "value": "MIMO_SA", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "ssid", "value": "ghy-090113-vAlzBmOj4MOxvPPAWkWJ9RrzSTw0mk-__-*********-__-1788282985880--RRR_0-RRR_0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "ftid", "value": "d0qvaWRAdL0jjjkNsAKEoVDmbX7EKgMw-1693588135653", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "orguserid", "value": "Zh47904Th4ZZ", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "orguseridp", "value": "*********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:16:54.151Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged<PERSON><PERSON>back", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-11T15:30:06.639Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cp", "value": "********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-10-01T17:16:54.935Z", "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_hjSessionUser_720738", "value": "eyJpZCI6IjE1MGZlMjkyLWJhY2EtNWM4MC1iYjJkLWFhY2M0MzQ2NWYzYiIsImNyZWF0ZWQiOjE2OTM1ODgxNjA2NTgsImV4aXN0aW5nIjp0cnVlfQ==", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-03T15:24:28.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_gcl_au", "value": "1.1.1845951909.1693588653", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-11-30T17:17:32.000Z", "httpOnly": false, "secure": false}, {"name": "_mshops_ga", "value": "GA1.3.326949057.1693588668", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-05T17:17:48.224Z", "httpOnly": false, "secure": false}, {"name": "_gid", "value": "GA1.3.790250885.1693826240", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:15:39.000Z", "httpOnly": false, "secure": false}, {"name": "_hjSession_580848", "value": "eyJpZCI6ImZhNzFlYWI2LTAwOTEtNDcwNy1iMzM0LTBjZmQ2MWYwMDIxOSIsImNyZWF0ZWQiOjE2OTM4MzQ2MDQzODgsImluU2FtcGxlIjp0cnVlfQ==", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T16:00:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjAbsoluteSessionInProgress", "value": "0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T16:00:01.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_mldataSessionId", "value": "5a012e2c-f590-42b7-8230-463254702ca7", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T16:00:06.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_ml_ga_gid", "value": "GA1.3.938323594.1693837590", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:27:00.000Z", "httpOnly": false, "secure": false}, {"name": "_hjIncludedInSessionSample_580848", "value": "1", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:31:57.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_hjSession_720738", "value": "********************************************************************************************************************", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:54:28.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_csrf", "value": "OgDIYUDrLsCCDqgY32XGFCa8", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "1969-12-31T23:59:59.000Z", "httpOnly": true, "secure": true}, {"name": "_ga", "value": "GA1.1.*********.**********", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:24:41.077Z", "httpOnly": false, "secure": false}, {"name": "_uetsid", "value": "2af819a04b3711eeb9dfed55c48f6324", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-05T15:24:41.000Z", "httpOnly": false, "secure": false}, {"name": "_uetvid", "value": "705460a048eb11eea11f8dc2e024e981", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-09-28T15:24:41.000Z", "httpOnly": false, "secure": false}, {"name": "_ga_NDJFKMJ2PD", "value": "GS1.1.**********.2.1.**********.49.0.0", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2024-10-08T15:24:52.769Z", "httpOnly": false, "secure": false}, {"name": "_hjShownFeedbackMessage", "value": "true", "path": "/", "domain": "myaccount.mercadolivre.com.br", "expires": "2023-09-05T15:25:51.000Z", "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "cookiesPreferencesLogged", "value": "%7B%22userId%22%3A*********%2C%22categories%22%3A%7B%22advertising%22%3Atrue%2C%22functionality%22%3Anull%2C%22performance%22%3Anull%2C%22traceability%22%3Anull%7D%7D", "path": "/", "domain": ".mercadolivre.com.br", "expires": "2023-09-04T15:35:06.638Z", "httpOnly": false, "secure": true, "sameSite": "None"}], "headersSize": -1, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "h3", "headers": [{"name": "accept-ch", "value": "device-memory, dpr, viewport-width, rtt, downlink, ect, save-data"}, {"name": "accept-ch-lifetime", "value": "60"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=86400"}, {"name": "cache-control", "value": "private, max-age=0, no-cache, no-store, must-revalidate"}, {"name": "content-disposition", "value": "inline; filename=MIMOIMPEEXPLTDA_********_********.zip"}, {"name": "content-type", "value": "application/zip"}, {"name": "date", "value": "Mon, 04 Sep 2023 15:29:40 GMT"}, {"name": "expect-ct", "value": "max-age=0"}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "strict-transport-security", "value": "max-age=15552000; includeSubDomains"}, {"name": "via", "value": "1.1 88ed6867c0889ee2caec1c3973510680.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "ZTpTsEUIcFL6P7qsWdiar0zm3BCiJX_fRBq2CbCD3owfhbxC4OFVhg=="}, {"name": "x-amz-cf-pop", "value": "GRU3-C1"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-d2id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408"}, {"name": "x-dns-prefetch-control", "value": "on"}, {"name": "x-download-options", "value": "noopen"}, {"name": "x-envoy-upstream-service-time", "value": "405"}, {"name": "x-permitted-cross-domain-policies", "value": "none"}, {"name": "x-request-device-id", "value": "fc6aa148-edc3-4dec-a790-2b7aebcf2408"}, {"name": "x-request-id", "value": "08a66ee4-4603-465b-a6ec-8064dec6d13e"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 0, "mimeType": "application/zip"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 666, "_error": "net::ERR_ABORTED"}, "serverIPAddress": "***********", "startedDateTime": "2023-09-04T15:30:06.661Z", "time": 562.2180000009394, "timings": {"blocked": 5.044000000517349, "dns": -1, "ssl": -1, "connect": -1, "send": 0.509, "wait": 541.8919999996351, "receive": 14.773000000786851, "_blocked_queueing": 4.34300000051735}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "i", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 15518}, {"functionName": "doSendTrack", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 19011}, {"functionName": "", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 18741}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "doSend", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 18717}, {"functionName": "", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 17001}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "sendTrackFromBrowser", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 16934}, {"functionName": "send", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 17488}, {"functionName": "c", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 21341}, {"functionName": "r.send", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 21956}, {"functionName": "e.<computed>", "scriptId": "45", "url": "https://http2.mlstatic.com/storage/melidata-js-sdk/js/3/0.4.0/melidata.min.js", "lineNumber": 0, "columnNumber": 24730}, {"functionName": "t.default", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 356981}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420438}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "value", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 420275}, {"functionName": "onClick", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 22104}, {"functionName": "We", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1003929}, {"functionName": "qe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1004083}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022286}, {"functionName": "<PERSON>", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022380}, {"functionName": "Tr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1022794}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1028437}, {"functionName": "Ne", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103773}, {"functionName": "", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024260}, {"functionName": "Cr", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1024290}, {"functionName": "Xt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1012350}, {"functionName": "Zt", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011576}, {"functionName": "t.unstable_runWithPriority", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1136341}, {"functionName": "Vo", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1032618}, {"functionName": "Pe", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1103513}, {"functionName": "$t", "scriptId": "53", "url": "https://http2.mlstatic.com/storage/si-documents-frontend/nfe.8943b078.js", "lineNumber": 1, "columnNumber": 1011369}, {"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}]}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "118661", "request": {"method": "POST", "url": "https://api.mercadolibre.com/tracks", "httpVersion": "http/2.0", "headers": [{"name": ":authority", "value": "api.mercadolibre.com"}, {"name": ":method", "value": "POST"}, {"name": ":path", "value": "/tracks"}, {"name": ":scheme", "value": "https"}, {"name": "accept", "value": "*/*"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "content-length", "value": "4383"}, {"name": "content-type", "value": "text/plain;charset=UTF-8"}, {"name": "origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "referer", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}, {"name": "sec-fetch-dest", "value": "empty"}, {"name": "sec-fetch-mode", "value": "cors"}, {"name": "sec-fetch-site", "value": "cross-site"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}], "queryString": [], "cookies": [], "headersSize": -1, "bodySize": 4383, "postData": {"mimeType": "text/plain;charset=UTF-8", "text": "{\"tracks\":[{\"path\":\"/myml/invoices/documents/nfe/download/validate/empty\",\"user\":{\"uid\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"user_id\":*********,\"user_tags\":[],\"privacy_consents\":{\"advertising\":true,\"traceability\":true,\"performance\":true,\"functionality\":true},\"session_id\":\"5a012e2c-f590-42b7-8230-463254702ca7\"},\"type\":\"event\",\"user_local_timestamp\":\"2023-09-04T12:30:06.649-0300\",\"server_id\":\"4eb7a80b-0787-4644-951a-502151998d5e\",\"id\":\"32c743c9-0b28-4fd0-16df-56c390d63fc7\",\"event_data\":{\"empty\":false,\"file_types\":\"xml\",\"simple_folder\":false,\"end\":\"********\",\"start\":\"********\",\"sale\":\"all\",\"returns\":\"all\",\"full\":\"all\",\"others\":\"all\"},\"platform\":{\"http\":{\"cookies\":{\"_d2id\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"cp\":\"********\",\"orguseridp\":\"*********\"},\"headers\":{\"host\":\"myaccount.mercadolivre.com.br\",\"x-request-id\":\"b03c2714-d4ba-41ee-a586-837f153f752a\",\"x-device-js\":\"true\",\"x-d2id\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"x-platform\":\"ml\"},\"http_url\":\"https://myaccount.mercadolivre.com.br/invoices/documents/nfe\",\"http_referer\":\"https://myaccount.mercadolivre.com.br/invoices/documents/type\",\"utm\":{},\"intersection_observer_supported\":true}},\"application\":{\"sdk_version\":{\"node_version\":\"4.7.4\",\"js_version\":\"0.4.0\"},\"version\":\"3.6.3\",\"business\":\"mercadolibre\",\"site_id\":\"MLB\",\"server_hostname\":\"\",\"server_poolname\":\"\",\"app_name\":\"\",\"secured\":{\"encrypted\":\"bc5770e8a939fd00af271830c62bbb58:10e60839301996943970fa3bfa9e8fd374d86d683374010f4276d1cb7be08dd075314cdca3329be54c7b83ea2d367ba9f521f04197d74e135ee12549fe846f2abe367c622e12f39f952017b78472a4464f90efaf04689be718d649192f2fdbd7b635598fe8f545f2740ae45d2f84222da1faf348f934b326eb5e4c3c5672f73d259854017c93a71743a98174cbca02445d7428e30b7cb581\",\"signature\":\"eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.QqFf5iME7T5b7JihkJXNzqTvuNJUrWAgVt4RWUmygSU\"}},\"device\":{\"resolution_height\":816,\"resolution_width\":1536,\"platform\":\"/web/desktop\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69\"},\"experiments\":{},\"created_hidden\":false},{\"path\":\"/myml/invoices/documents/nfe/download/start\",\"user\":{\"uid\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"user_id\":*********,\"user_tags\":[],\"privacy_consents\":{\"advertising\":true,\"traceability\":true,\"performance\":true,\"functionality\":true},\"session_id\":\"5a012e2c-f590-42b7-8230-463254702ca7\"},\"type\":\"event\",\"user_local_timestamp\":\"2023-09-04T12:30:06.652-0300\",\"server_id\":\"4eb7a80b-0787-4644-951a-502151998d5e\",\"id\":\"6172bfb7-d221-4454-0e8a-b553a52d7b4d\",\"event_data\":{\"file_types\":\"xml\",\"simple_folder\":false,\"end\":\"********\",\"start\":\"********\",\"sale\":\"all\",\"returns\":\"all\",\"full\":\"all\",\"others\":\"all\"},\"platform\":{\"http\":{\"cookies\":{\"_d2id\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"cp\":\"********\",\"orguseridp\":\"*********\"},\"headers\":{\"host\":\"myaccount.mercadolivre.com.br\",\"x-request-id\":\"b03c2714-d4ba-41ee-a586-837f153f752a\",\"x-device-js\":\"true\",\"x-d2id\":\"fc6aa148-edc3-4dec-a790-2b7aebcf2408\",\"x-platform\":\"ml\"},\"http_url\":\"https://myaccount.mercadolivre.com.br/invoices/documents/nfe\",\"http_referer\":\"https://myaccount.mercadolivre.com.br/invoices/documents/type\",\"utm\":{},\"intersection_observer_supported\":true}},\"application\":{\"sdk_version\":{\"node_version\":\"4.7.4\",\"js_version\":\"0.4.0\"},\"version\":\"3.6.3\",\"business\":\"mercadolibre\",\"site_id\":\"MLB\",\"server_hostname\":\"\",\"server_poolname\":\"\",\"app_name\":\"\",\"secured\":{\"encrypted\":\"bc5770e8a939fd00af271830c62bbb58:10e60839301996943970fa3bfa9e8fd374d86d683374010f4276d1cb7be08dd075314cdca3329be54c7b83ea2d367ba9f521f04197d74e135ee12549fe846f2abe367c622e12f39f952017b78472a4464f90efaf04689be718d649192f2fdbd7b635598fe8f545f2740ae45d2f84222da1faf348f934b326eb5e4c3c5672f73d259854017c93a71743a98174cbca02445d7428e30b7cb581\",\"signature\":\"eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************.QqFf5iME7T5b7JihkJXNzqTvuNJUrWAgVt4RWUmygSU\"}},\"device\":{\"resolution_height\":816,\"resolution_width\":1536,\"platform\":\"/web/desktop\",\"user_agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69\"},\"experiments\":{},\"created_hidden\":false}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "http/2.0", "headers": [{"name": "access-control-allow-headers", "value": "Content-Type"}, {"name": "access-control-allow-methods", "value": "PUT, GET, POST, DELETE, OPTIONS"}, {"name": "access-control-allow-origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "access-control-max-age", "value": "86400"}, {"name": "content-encoding", "value": "gzip"}, {"name": "content-type", "value": "application/json"}, {"name": "date", "value": "Mon, 04 Sep 2023 15:29:39 GMT"}, {"name": "vary", "value": "Accept-Encoding, User-Agent"}, {"name": "via", "value": "1.1 e6327b665af749287dd289cd3e060bec.cloudfront.net (CloudFront)"}, {"name": "x-amz-cf-id", "value": "msC2leCLy3Z-liXAHTSFr1bbDqs5rDgG3iTYTEOVkHiU6074ryfpTw=="}, {"name": "x-amz-cf-pop", "value": "GRU3-P3"}, {"name": "x-cache", "value": "Miss from cloudfront"}, {"name": "x-content-type-options", "value": "nosniff"}, {"name": "x-frame-options", "value": "DENY"}, {"name": "x-request-id", "value": "8c5c30cc-df69-4cda-a304-f1dcf3778890"}, {"name": "x-xss-protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 2208, "mimeType": "application/json", "text": "{\"records\":[{\"status\":200},{\"status\":200}],\"configuration\":{\"batch_size\":40,\"batch_size_wifi\":80,\"blocklist\":\"[\\\"/cards/nfc/constraint/update/\\\",\\\"/cards/nfc/core/service/error\\\",\\\"/auth/attestation/nonce/fail\\\",\\\"/cross_app_links/fetch_time\\\",\\\"/cards/nfc/feature/availability\\\",\\\"cards/nfc/feature/availability\\\"]\",\"blocklistV2\":\"[{\\\"path\\\": \\\"/cards/nfc/constraint/update/\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cards/nfc/core/service/error\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/auth/attestation/nonce/fail\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.201.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cross_app_links/fetch_time\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cross_app_links/fetch_time\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.196.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/cards/nfc/feature/availability\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"2.256.0\\\"},{\\\"path\\\": \\\"cards/nfc/feature/availability\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"2.256.0\\\"},{\\\"path\\\": \\\"/navigation/link_monitoring\\\",\\\"business\\\": \\\"mercadolibre\\\",\\\"versionFrom\\\": \\\"10.201.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/navigation/link_monitoring\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/sign_in/sso\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/flow/friction/networking\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"},{\\\"path\\\": \\\"/flow/friction/system\\\",\\\"business\\\": \\\"mercadopago\\\",\\\"versionFrom\\\": \\\"2.206.0\\\",\\\"versionTo\\\": \\\"\\\"}]\",\"bulk_size\":10,\"bulk_size_wifi\":20,\"database_status_check_interval\":30,\"experiment_storage_enabled\":\"true\",\"geo_track_enabled_android_version\":\"8.4.0\",\"geo_track_enabled_ios_version\":\"0.15.0\",\"geolocation_max_hours\":4,\"pagination_enabled_android_version\":\"8.0.0\",\"pagination_enabled_ios_version\":\"10.0.0\",\"pagination_page_size\":50,\"scheduled_dispatch_interval\":5},\"failed_record_count\":0}"}, "redirectURL": "", "headersSize": -1, "bodySize": -1, "_transferSize": 1072, "_error": null}, "serverIPAddress": "***************", "startedDateTime": "2023-09-04T15:30:06.678Z", "time": 215.**************, "timings": {"blocked": 5.***************, "dns": 25.608, "ssl": 5.***************, "connect": 36.***************, "send": 0.*****************, "wait": 147.**************, "receive": 0.***************, "_blocked_queueing": 5.***************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}}}}}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "118582", "request": {"method": "POST", "url": "https://bam.nr-data.net/jserrors/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=251056&ck=0&s=9e90a2557b633988&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=8b649efe-0001-ba39-97a6-018a60cc82fc", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "921"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "251056"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "9e90a2557b633988"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "8b649efe-0001-ba39-97a6-018a60cc82fc"}], "cookies": [], "headersSize": 969, "bodySize": 921, "postData": {"mimeType": "text/plain", "text": "{\"xhr\":[{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":610},\"rxSize\":{\"t\":24},\"duration\":{\"t\":191},\"cbTime\":{\"t\":0},\"time\":{\"t\":241050}}},{\"params\":{\"method\":\"POST\",\"hostname\":\"bam.nr-data.net\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"bam.nr-data.net:443\",\"pathname\":\"/resources/1/NRBR-766f4fb616d3a2368ce\",\"status\":200},\"metrics\":{\"count\":1,\"txSize\":{\"t\":27807},\"rxSize\":{\"t\":36},\"duration\":{\"t\":219},\"cbTime\":{\"t\":0},\"time\":{\"t\":241217}}},{\"params\":{\"method\":\"GET\",\"hostname\":\"myaccount.mercadolivre.com.br\",\"port\":\"443\",\"protocol\":\"https\",\"host\":\"myaccount.mercadolivre.com.br:443\",\"pathname\":\"/invoices/documents/api/nfe/zip/validate\",\"status\":200},\"metrics\":{\"count\":1,\"rxSize\":{\"t\":15},\"duration\":{\"t\":846},\"cbTime\":{\"t\":0},\"time\":{\"t\":249343}}}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "CF-Cache-Status", "value": "DYNAMIC"}, {"name": "CF-Ray", "value": "801744f3aa3e1b2e-GRU"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "Content-Type", "value": "image/gif"}, {"name": "Date", "value": "Mon, 04 Sep 2023 15:29:40 GMT"}, {"name": "Server", "value": "cloudflare"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 393, "bodySize": 24, "_transferSize": 417, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-09-04T15:30:07.536Z", "time": 208.**************, "timings": {"blocked": 6.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 201.**************, "receive": 0.****************, "_blocked_queueing": 6.***************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "send", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3981}, {"functionName": "i.length.a", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1545}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1355}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}}}}}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "118465", "request": {"method": "POST", "url": "https://bam.nr-data.net/events/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=251059&ck=0&s=9e90a2557b633988&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=8b649efe-0001-ba39-97a6-018a60cc82fc", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "114"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "251059"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "9e90a2557b633988"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "8b649efe-0001-ba39-97a6-018a60cc82fc"}], "cookies": [], "headersSize": 967, "bodySize": 114, "postData": {"mimeType": "text/plain", "text": "bel.7;2,,5ce7,mx,,,'GET,5k,'myaccount.mercadolivre.com.br:443,'/invoices/documents/api/nfe/zip/validate,,f,,'0,!!!"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "CF-Cache-Status", "value": "DYNAMIC"}, {"name": "CF-Ray", "value": "801744f3ba951b27-GRU"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "24"}, {"name": "Content-Type", "value": "image/gif"}, {"name": "Date", "value": "Mon, 04 Sep 2023 15:29:40 GMT"}, {"name": "Server", "value": "cloudflare"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}], "cookies": [], "content": {"size": 24, "mimeType": "image/gif", "compression": 0, "text": "R0lGODlhAQABAAAAACwAAAAAAQABAAAC", "encoding": "base64"}, "redirectURL": "", "headersSize": 393, "bodySize": 24, "_transferSize": 417, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-09-04T15:30:07.541Z", "time": 199.**************, "timings": {"blocked": 13.**************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.*****************, "wait": 185.*************, "receive": 0.*****************, "_blocked_queueing": 13.***************}}, {"_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "nrWrapper", "scriptId": "36", "url": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe", "lineNumber": 4, "columnNumber": 14738}, {"functionName": "i", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 15724}, {"functionName": "_send", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 5519}, {"functionName": "sendX", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 3780}, {"functionName": "a", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1470}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1583}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1570}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}], "parent": {"description": "setTimeout", "callFrames": [{"functionName": "scheduleHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 989}, {"functionName": "runHarvest", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1726}, {"functionName": "", "scriptId": "34", "url": "https://js-agent.newrelic.com/nr-full.6ee41851-1.238.0.min.js", "lineNumber": 1, "columnNumber": 1035}]}}}}}}}}}}}}}}}}}}}}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "118577", "request": {"method": "POST", "url": "https://bam.nr-data.net/resources/1/NRBR-766f4fb616d3a2368ce?a=*********&v=1.238.0&to=YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D&rst=251229&ck=0&s=9e90a2557b633988&ref=https://myaccount.mercadolivre.com.br/invoices/documents/nfe&ptid=8b649efe-0001-ba39-97a6-018a60cc82fc&st=*************", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "29515"}, {"name": "Host", "value": "bam.nr-data.net"}, {"name": "Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.69"}, {"name": "content-type", "value": "text/plain"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Microsoft Edge\";v=\"116\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [{"name": "a", "value": "*********"}, {"name": "v", "value": "1.238.0"}, {"name": "to", "value": "YlZQYEVZC0QEV0BZV1scd0xHSgBEFl5HH39wZx0bXlYTWAxXUUMXUVxRQVpdC0MWG1pWXRo%3D"}, {"name": "rst", "value": "251229"}, {"name": "ck", "value": "0"}, {"name": "s", "value": "9e90a2557b633988"}, {"name": "ref", "value": "https://myaccount.mercadolivre.com.br/invoices/documents/nfe"}, {"name": "ptid", "value": "8b649efe-0001-ba39-97a6-018a60cc82fc"}, {"name": "st", "value": "*************"}], "cookies": [], "headersSize": 989, "bodySize": 29515, "postData": {"mimeType": "text/plain", "text": "{\"res\":[{\"n\":\"load\",\"s\":241241,\"e\":241241,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":241241,\"e\":241241,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":241241,\"e\":241241,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":241436,\"e\":241436,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":241436,\"e\":241436,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":241436,\"e\":241436,\"t\":\"event\",\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\"},{\"n\":\"load\",\"s\":250189,\"e\":250189,\"t\":\"event\",\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\"},{\"n\":\"load\",\"s\":250189,\"e\":250189,\"t\":\"event\",\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\"},{\"n\":\"Ajax\",\"s\":241050,\"e\":241241,\"o\":\"200 POST: bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":241217,\"e\":241436,\"o\":\"200 POST: bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"ajax\"},{\"n\":\"Ajax\",\"s\":249343,\"e\":250189,\"o\":\"200 GET: myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\",\"t\":\"ajax\"},{\"n\":\"xmlhttprequest\",\"s\":241050,\"e\":241238,\"o\":\"https://bam.nr-data.net:443/jserrors/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":241218,\"e\":241434,\"o\":\"https://bam.nr-data.net:443/resources/1/NRBR-766f4fb616d3a2368ce\",\"t\":\"resource\"},{\"n\":\"xmlhttprequest\",\"s\":249343,\"e\":250162,\"o\":\"https://myaccount.mercadolivre.com.br:443/invoices/documents/api/nfe/zip/validate\",\"t\":\"resource\"},{\"n\":\"pointermove\",\"s\":247702,\"e\":247702,\"t\":\"event\",\"o\":\"div.nav-footer-user-info.nav-bounds\"},{\"n\":\"pointermove\",\"s\":247705,\"e\":247705,\"t\":\"event\",\"o\":\"div.nav-footer-user-info.nav-bounds\"},{\"n\":\"pointermove\",\"s\":247716,\"e\":247716,\"t\":\"event\",\"o\":\"div.nav-footer-info-wrapper\"},{\"n\":\"pointermove\",\"s\":247718,\"e\":247718,\"t\":\"event\",\"o\":\"small.nav-footer-copyright\"},{\"n\":\"pointermove\",\"s\":247729,\"e\":247729,\"t\":\"event\",\"o\":\"ul.nav-footer-navigation__menu\"},{\"n\":\"pointermove\",\"s\":247738,\"e\":247738,\"t\":\"event\",\"o\":\"ul.nav-footer-navigation__menu\"},{\"n\":\"pointermove\",\"s\":247745,\"e\":247745,\"t\":\"event\",\"o\":\"div.nav-footer-user-info.nav-bounds\"},{\"n\":\"pointermove\",\"s\":247755,\"e\":247755,\"t\":\"event\",\"o\":\"div.nav-footer-user-info.nav-bounds\"},{\"n\":\"pointermove\",\"s\":247758,\"e\":247758,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247758,\"e\":247758,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247758,\"e\":247758,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247769,\"e\":247769,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247769,\"e\":247769,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247769,\"e\":247769,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247780,\"e\":247780,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247780,\"e\":247780,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247780,\"e\":247780,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247784,\"e\":247784,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247784,\"e\":247784,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247784,\"e\":247784,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247795,\"e\":247795,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247795,\"e\":247795,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247795,\"e\":247795,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247800,\"e\":247800,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247801,\"e\":247801,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247801,\"e\":247801,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247811,\"e\":247811,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247811,\"e\":247811,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247811,\"e\":247811,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247821,\"e\":247821,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247821,\"e\":247821,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247821,\"e\":247821,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247825,\"e\":247825,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247825,\"e\":247825,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247825,\"e\":247825,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247835,\"e\":247835,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247835,\"e\":247835,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247835,\"e\":247835,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247841,\"e\":247841,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247841,\"e\":247841,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247841,\"e\":247841,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247851,\"e\":247851,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247851,\"e\":247851,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247851,\"e\":247851,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247861,\"e\":247861,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247861,\"e\":247861,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247861,\"e\":247861,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247864,\"e\":247864,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247864,\"e\":247864,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247864,\"e\":247864,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247877,\"e\":247877,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247877,\"e\":247877,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247877,\"e\":247877,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247881,\"e\":247881,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247881,\"e\":247881,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247881,\"e\":247881,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointermove\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointermove\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointermove\",\"s\":247902,\"e\":247902,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247902,\"e\":247902,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247902,\"e\":247902,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247906,\"e\":247906,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247906,\"e\":247906,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247906,\"e\":247907,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247916,\"e\":247916,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247916,\"e\":247916,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247916,\"e\":247916,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247922,\"e\":247922,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247922,\"e\":247922,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247922,\"e\":247922,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247931,\"e\":247931,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247931,\"e\":247931,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247931,\"e\":247931,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247941,\"e\":247941,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247941,\"e\":247941,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":247941,\"e\":247941,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248023,\"e\":248023,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248023,\"e\":248023,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248024,\"e\":248024,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248027,\"e\":248027,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248027,\"e\":248027,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248027,\"e\":248027,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248038,\"e\":248038,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248038,\"e\":248038,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248038,\"e\":248038,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248043,\"e\":248043,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248043,\"e\":248043,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248043,\"e\":248043,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248054,\"e\":248054,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248054,\"e\":248054,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248054,\"e\":248054,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248063,\"e\":248063,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248063,\"e\":248063,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248063,\"e\":248063,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointermove\",\"s\":248069,\"e\":248069,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248069,\"e\":248069,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248069,\"e\":248069,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248077,\"e\":248077,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248077,\"e\":248077,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248077,\"e\":248077,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248083,\"e\":248083,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248083,\"e\":248083,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248083,\"e\":248083,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248093,\"e\":248093,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248093,\"e\":248093,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248093,\"e\":248093,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248104,\"e\":248104,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248104,\"e\":248104,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248104,\"e\":248104,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248109,\"e\":248109,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248109,\"e\":248109,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248109,\"e\":248109,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248118,\"e\":248118,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248118,\"e\":248118,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248118,\"e\":248118,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248123,\"e\":248123,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248123,\"e\":248123,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248123,\"e\":248123,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248134,\"e\":248134,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248134,\"e\":248134,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248134,\"e\":248134,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248143,\"e\":248143,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248143,\"e\":248143,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248143,\"e\":248143,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248150,\"e\":248150,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248151,\"e\":248151,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248151,\"e\":248151,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248159,\"e\":248159,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248160,\"e\":248160,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248160,\"e\":248160,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248174,\"e\":248174,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248174,\"e\":248174,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248174,\"e\":248174,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248224,\"e\":248224,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248224,\"e\":248224,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":248224,\"e\":248224,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointermove\",\"s\":250122,\"e\":250122,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250122,\"e\":250122,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250122,\"e\":250122,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250132,\"e\":250132,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250132,\"e\":250132,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250132,\"e\":250132,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250141,\"e\":250141,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250141,\"e\":250142,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250142,\"e\":250142,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250145,\"e\":250145,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250146,\"e\":250146,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250146,\"e\":250146,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250156,\"e\":250156,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250156,\"e\":250156,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250156,\"e\":250156,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250161,\"e\":250161,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250161,\"e\":250161,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250161,\"e\":250161,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointermove\",\"s\":250193,\"e\":250193,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250193,\"e\":250193,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250193,\"e\":250193,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250201,\"e\":250201,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250201,\"e\":250201,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250201,\"e\":250201,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointermove\",\"s\":250202,\"e\":250202,\"t\":\"event\",\"o\":\"ul.nav-footer-navigation__menu\"},{\"n\":\"pointermove\",\"s\":250211,\"e\":250211,\"t\":\"event\",\"o\":\"small.nav-footer-copyright\"},{\"n\":\"pointermove\",\"s\":250220,\"e\":250220,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointermove\",\"s\":250220,\"e\":250220,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointermove\",\"s\":250220,\"e\":250220,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointermove\",\"s\":250226,\"e\":250226,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointermove\",\"s\":250226,\"e\":250226,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointermove\",\"s\":250227,\"e\":250227,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"mousing\",\"s\":247702,\"e\":247755,\"t\":\"event\",\"o\":\"div.nav-footer-user-info.nav-bounds\"},{\"n\":\"mousing\",\"s\":247716,\"e\":247716,\"t\":\"event\",\"o\":\"div.nav-footer-info-wrapper\"},{\"n\":\"mousing\",\"s\":247718,\"e\":247718,\"t\":\"event\",\"o\":\"small.nav-footer-copyright\"},{\"n\":\"mousing\",\"s\":250212,\"e\":250212,\"t\":\"event\",\"o\":\"small.nav-footer-copyright\"},{\"n\":\"mousing\",\"s\":247729,\"e\":247738,\"t\":\"event\",\"o\":\"ul.nav-footer-navigation__menu\"},{\"n\":\"mousing\",\"s\":250202,\"e\":250202,\"t\":\"event\",\"o\":\"ul.nav-footer-navigation__menu\"},{\"n\":\"mousing\",\"s\":247758,\"e\":247891,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"mousing\",\"s\":250193,\"e\":250202,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"mousing\",\"s\":247891,\"e\":247901,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"mousing\",\"s\":247901,\"e\":248068,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"mousing\",\"s\":248068,\"e\":248224,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"mousing\",\"s\":249346,\"e\":249346,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"mousing\",\"s\":249346,\"e\":250161,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"mousing\",\"s\":250220,\"e\":250237,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointerover\",\"s\":247758,\"e\":247758,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":247758,\"e\":247758,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointerover\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointerover\",\"s\":247901,\"e\":247901,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerover\",\"s\":247901,\"e\":247901,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerover\",\"s\":248068,\"e\":248068,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerover\",\"s\":248068,\"e\":248068,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerover\",\"s\":249346,\"e\":249346,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointerover\",\"s\":249346,\"e\":249346,\"t\":\"event\",\"o\":\"div.andes-spinner__mask\"},{\"n\":\"pointerover\",\"s\":250193,\"e\":250193,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":250193,\"e\":250193,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerover\",\"s\":250220,\"e\":250220,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointerover\",\"s\":250220,\"e\":250220,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointerout\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":247891,\"e\":247891,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":247901,\"e\":247901,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointerout\",\"s\":247901,\"e\":247901,\"t\":\"event\",\"o\":\"div.action-buttons\"},{\"n\":\"pointerout\",\"s\":248068,\"e\":248068,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerout\",\"s\":248068,\"e\":248068,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerout\",\"s\":249345,\"e\":249345,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerout\",\"s\":249345,\"e\":249346,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerout\",\"s\":250202,\"e\":250202,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":250202,\"e\":250202,\"t\":\"event\",\"o\":\"section.si-main-content.si-main-content--modifier\"},{\"n\":\"pointerout\",\"s\":250237,\"e\":250237,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"pointerout\",\"s\":250237,\"e\":250237,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"transitionrun\",\"s\":247915,\"e\":247915,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":249249,\"e\":249249,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionrun\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":248099,\"e\":248099,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":248099,\"e\":248099,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":248099,\"e\":248099,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":249534,\"e\":249534,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":249534,\"e\":249534,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"transitionend\",\"s\":249534,\"e\":249534,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focus\",\"s\":249233,\"e\":249238,\"t\":\"event\",\"o\":\"window\"},{\"n\":\"focus\",\"s\":249239,\"e\":249239,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerdown\",\"s\":249238,\"e\":249238,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerdown\",\"s\":249238,\"e\":249238,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerdown\",\"s\":249238,\"e\":249238,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"focusin\",\"s\":249239,\"e\":249239,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"focusin\",\"s\":249239,\"e\":249239,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"pointerup\",\"s\":249323,\"e\":249323,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":249323,\"e\":249323,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":249323,\"e\":249323,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"pointerup\",\"s\":249323,\"e\":249323,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249323,\"e\":249324,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249324,\"e\":249324,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249324,\"e\":249324,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249324,\"e\":249339,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"click\",\"s\":249344,\"e\":249344,\"t\":\"event\",\"o\":\"span.andes-button__content\"},{\"n\":\"transitioncancel\",\"s\":249346,\"e\":249346,\"t\":\"event\",\"o\":\"button.andes-button.action-export__btn-download.andes-button--large.andes-button--loud\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon.andes-spinner__icon--large\"},{\"n\":\"animationstart\",\"s\":249349,\"e\":249349,\"t\":\"event\",\"o\":\"div.andes-spinner__icon.andes-spinner__icon--large\"},{\"n\":\"animationstart\",\"s\":250227,\"e\":250227,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationstart\",\"s\":250227,\"e\":250227,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationiteration\",\"s\":250083,\"e\":250083,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":250083,\"e\":250083,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":250083,\"e\":250083,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationiteration\",\"s\":250083,\"e\":250083,\"t\":\"event\",\"o\":\"div.andes-spinner__icon-border\"},{\"n\":\"animationend\",\"s\":250703,\"e\":250703,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"},{\"n\":\"animationend\",\"s\":250703,\"e\":250706,\"t\":\"event\",\"o\":\"div.andes-snackbar.andes-snackbar--success.andes-snackbar--right.andes-snackbar--animate-show\"}]}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "https://myaccount.mercadolivre.com.br"}, {"name": "CF-Cache-Status", "value": "DYNAMIC"}, {"name": "CF-Ray", "value": "801744f4cb6ba597-GRU"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "36"}, {"name": "Content-Type", "value": "text/plain"}, {"name": "Date", "value": "Mon, 04 Sep 2023 15:29:40 GMT"}, {"name": "Server", "value": "cloudflare"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "access-control-allow-credentials", "value": "true"}, {"name": "access-control-allow-methods", "value": "GET, POST, PUT, HEAD, OPTIONS"}], "cookies": [], "content": {"size": 36, "mimeType": "text/plain", "compression": 0, "text": "8b649efe-0001-ba39-97a6-018a60cc82fc"}, "redirectURL": "", "headersSize": 394, "bodySize": 36, "_transferSize": 430, "_error": null}, "serverIPAddress": "**************", "startedDateTime": "2023-09-04T15:30:07.709Z", "time": 224.*************, "timings": {"blocked": 3.***************, "dns": -1, "ssl": -1, "connect": -1, "send": 0.37399999999999994, "wait": 220.70599999921606, "receive": 0.4690000005211914, "_blocked_queueing": 2.967000000353437}}]}}