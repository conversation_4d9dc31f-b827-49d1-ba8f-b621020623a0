let results = {};
let uniqueKeys = new Set();

for (let item of items) {
    const xmlData = item.json; // Usar a estrutura de dados do item atual

    if (xmlData && xmlData["nfeProc"] && xmlData["nfeProc"]["NFe"] && xmlData["nfeProc"]["NFe"]["infNFe"] && xmlData["nfeProc"]["NFe"]["infNFe"]["dest"]) {
        const dest = xmlData["nfeProc"]["NFe"]["infNFe"]["dest"];
        const key = dest["CNPJ"] || dest["CPF"];
        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal
        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes('mercadolivre')
            ? 'CML'
            : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes('Bling')
            ? 'D2C'
            : '';
        // Novas variáveis
        const tpNF = xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'];
        const cStat = xmlData['nfeProc']['protNFe']['infProt']['cStat'];
        const natOp = xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'];

        if (key && CODCOB && tpNF && cStat && natOp) {
            if (!uniqueKeys.has(key)) {
                uniqueKeys.add(key);
                if (!results[key]) {
                    results[key] = {
                        CGCENT: null,
                        CONSUMIDORFINAL: 'N',
                        CONTRIBUIENTE: 'S',
                        TIPOFJ: 'J',
                        xMun: dest["enderDest"] && dest["enderDest"]["xMun"] || '',
                        UF: dest["enderDest"] && dest["enderDest"]["UF"] || '',
                        CODCOB: CODCOB,
                        tpNF: tpNF, // Adicionando tpNF ao resultado
                        cStat: cStat, // Adicionando cStat ao resultado
                        natOp: natOp, // Adicionando natOp ao resultado
                        xNome: dest["xNome"] || '',
                        IE: dest["IE"] || 'ISENTO',
                        xLgr: dest["enderDest"] && dest["enderDest"]["xLgr"] || '',
                        nro: dest["enderDest"] && dest["enderDest"]["nro"] || '',
                        xBairro: dest["enderDest"] && dest["enderDest"]["xBairro"] || '',
                        CEP: dest["enderDest"] && dest["enderDest"]["CEP"] || '',
                        cMun: dest["enderDest"] && dest["enderDest"]["cMun"] || '',
                        cPais: dest["enderDest"] && dest["enderDest"]["cPais"] || '',
                    };

                    if (dest["CNPJ"]) {
                        results[key].CGCENT = dest["CNPJ"];
                        results[key].CONSUMIDORFINAL = 'N';
                        results[key].CONTRIBUIENTE = 'S';
                        results[key].TIPOFJ = 'J';
                    } else if (dest["CPF"]) {
                        results[key].CGCENT = dest["CPF"];
                        results[key].CONSUMIDORFINAL = 'S';
                        results[key].CONTRIBUIENTE = 'N';
                        results[key].TIPOFJ = 'F';
                    }
                }

                // Vincule o resultado ao item de entrada
                item.pairedItem = results[key];
            }
        }
    }
}

const uniqueResults = items
  .filter(item => item.pairedItem && item.pairedItem.CGCENT && item.pairedItem.CODCOB && item.pairedItem.tpNF && item.pairedItem.cStat && item.pairedItem.natOp)
  .map(item => {
    return {
        json: {
            CGCENT: item.pairedItem.CGCENT,
            CONSUMIDORFINAL: item.pairedItem.CONSUMIDORFINAL,
            CONTRIBUIENTE: item.pairedItem.CONTRIBUIENTE,
            TIPOFJ: item.pairedItem.TIPOFJ,
            xMun: item.pairedItem.xMun,
            UF: item.pairedItem.UF,
            CODCOB: item.pairedItem.CODCOB,
            tpNF: item.pairedItem.tpNF, // Incluindo tpNF nos resultados finais
            cStat: item.pairedItem.cStat, // Incluindo cStat nos resultados finais
            natOp: item.pairedItem.natOp, // Incluindo natOp nos resultados finais
            xNome: item.pairedItem.xNome, // Incluindo xNome nos resultados finais
            IE: item.pairedItem.IE, // Incluindo IE nos resultados finais
            xLgr: item.pairedItem.xLgr, // Incluindo xLgr nos resultados finais
            nro: item.pairedItem.nro, // Incluindo nro nos resultados finais
            xBairro: item.pairedItem.xBairro, // Incluindo xBairro nos resultados finais
            CEP: item.pairedItem.CEP, // Incluindo CEP nos resultados finais
            cMun: item.pairedItem.cMun, // Incluindo cMun nos resultados finais
            cPais: item.pairedItem.cPais, // Incluindo cPais nos resultados finais
        }
    };
});

return uniqueResults;
