{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [180, 0], "id": "70247c05-668f-455e-9c47-9180a6cebf3c", "name": "When clicking ‘Test workflow’"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [720, 0], "id": "768225c6-ec7a-4029-ac16-556cd9a5c9d7", "name": "XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [540, 0], "id": "60968758-93cd-484b-9dd1-b76f4a4fdd8b", "name": "Extract from File"}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/CANCELADAS/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [360, 0], "id": "b3f0ef64-143b-4a17-b53e-ee4dec654461", "name": "Read/Write Files from Disk"}, {"parameters": {"jsCode": "// NOTAS CANCELADAS\nconst items = $items(); // Obter todos os itens do nó anterior\n\nconst result = [];\n\n// Loop para filtrar notas canceladas e coletar informações adicionais\nitems.forEach(item => {\n  const xmlData = item.json;\n\n  // Verifica se é uma NF cancelada e extrai as informações\n  if (\n    xmlData &&\n    xmlData['procEventoNFe'] &&\n    xmlData['procEventoNFe']['evento'] &&\n    xmlData['procEventoNFe']['evento']['infEvento'] &&\n    xmlData['procEventoNFe']['evento']['infEvento']['chNFe']\n  ) {\n    const infEvento = xmlData['procEventoNFe']['evento']['infEvento'];\n    const retEvento = xmlData['procEventoNFe']['retEvento']['infEvento'];\n\n    // Verifica se o evento é um cancelamento (tipo de evento igual a \"110111\")\n    if (infEvento['tpEvento'] === '110111') {\n      const notaCancelada = {\n        chNFe: infEvento['chNFe'],\n        status: retEvento['cStat'] || 'Status não disponível',\n        statusDescricao: retEvento['xEvento'] || 'Descrição não disponível',\n        data: infEvento['dhEvento'] || 'Data não disponível',\n        CNPJ: infEvento['CNPJ'] || 'CNPJ não disponível',\n        CPF: retEvento['CPFDest'] || 'CPF não disponível',\n        justificativa: infEvento['detEvento']['xJust'] || 'Justificativa não disponível',\n      };\n\n      result.push({ json: notaCancelada });\n    }\n  }\n});\n\nreturn result;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 0], "id": "fc71879a-7499-47d9-8508-1a00fdf2937c", "name": "<PERSON><PERSON>"}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1080, 0], "id": "ebc55bbc-afc3-45f3-abc7-25fb4b58856c", "name": "Remove Duplicates"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n    NUMTRANSVENDA AS NUM_TRANSACAO,\n    NUMNOTA,\n    VLTOTAL,\n    'S' AS TIPO,\n    TO_CHAR(DTSAIDA,'DD/MM/YYYY') AS DATA,\n    CHAVENFE,\n    CODFILIAL,\n    CODCLI AS CODPARCEIRO,\n    (TO_CHAR(CODCLI) || '-' || (SELECT CLIENTE\n                                 FROM PCCLIENT\n                                 WHERE CODCLI = PCNFSAID.CODCLI)) AS PARCEIRO,\n    SYS_CONTEXT('USERENV', 'HOST') AS HOST,\n    SYS_CONTEXT('USERENV', 'TERMINAL') AS TERMINAL,\n    SYS_CONTEXT('USERENV', 'OS_USER') AS OS_USER\nFROM \n    PCNFSAID\nWHERE \n    DTCANCEL IS NULL\n    AND CHAVENFE = '{{ $json.chNFe }}'\nUNION ALL\nSELECT \n    NUMTRANSENT AS NUM_TRANSACAO,\n    NUMNOTA,\n    VLTOTAL,\n    'E' AS TIPO,\n    TO_CHAR(DTENT,'DD/MM/YYYY') AS DATA,\n    CHAVENFE,\n    CODFILIAL,\n    CODFORNEC AS CODPARCEIRO,\n    CA<PERSON> \n        WHEN TIPODESCARGA = '6' THEN (TO_CHAR(CODFORNEC) || '-' || FORNECEDOR)\n        WHEN TIPODESCARGA = 'R' THEN (TO_CHAR(CODFORNEC) || '-' || (SELECT FORNECEDOR\n                                      FROM PCFORNEC\n                                      WHERE CODFORNEC = PCNFENT.CODFORNEC))\n    END AS PARCEIRO,\n    SYS_CONTEXT('USERENV', 'HOST') AS HOST,\n    SYS_CONTEXT('USERENV', 'TERMINAL') AS TERMINAL,\n    SYS_CONTEXT('USERENV', 'OS_USER') AS OS_USER\nFROM \n    PCNFENT\nWHERE \n    DTCANCEL IS NULL\n    AND CHAVENFE = '{{ $json.chNFe }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1260, 0], "id": "f9e7c2d3-e38c-4dd1-b234-a2195c53c755", "name": "Validar NF", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ff48a308-4915-4b11-bcb8-0ff7ab1f0b17", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1440, 0], "id": "15614936-d56b-46d9-8f70-f8eda8d44495", "name": "If"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "6075f51c-f893-410d-b8ff-de773e972288", "leftValue": "={{ $json.TIPO }}", "rightValue": "S", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "SAIDA"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.TIPO }}", "rightValue": "E", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ENTRADA"}]}, "options": {}}, "id": "f2d5c8a9-1c79-4dc2-8492-e07bdf0f1a7b", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1640, 20]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n    pncodigofuncionario VARCHAR2(100);\n    pnnumtransvenda     NUMBER;\n    pvc2motivocancel    VARCHAR2(100);\n    pvc2menssagem       VARCHAR2(100);\nBEGIN\n    -- Atribuir valores aos parâmetros de entrada\n    pncodigofuncionario := 'PCADMIN';\n    pnnumtransvenda     := {{ $json.NUM_TRANSACAO }};\n    pvc2motivocancel    := 'CANCELADA NO MERCADO LIVRE';\n    pvc2menssagem       := 'OK';\n\n    -- Chamar a procedure\n    P_PC_CACELA_NF(\n        pncodigofuncionario => pncodigofuncionario,\n        pnnumtransvenda     => pnnumtransvenda,\n        pvc2motivocancel    => pvc2motivocancel,\n        pvc2menssagem       => pvc2menssagem\n    );\n    -- Exibir a mensagem de saída\n    DBMS_OUTPUT.PUT_LINE('Mensagem: ' || pvc2menssagem);\n    -- Realizar o commit se tudo estiver correto\n    COMMIT;\n\nEXCEPTION\n    WHEN OTHERS THEN\n    ROLLBACK;\n        -- Capt<PERSON>r qualquer outro erro\n        pvc2menssagem := SQLERRM;\n        DBMS_OUTPUT.PUT_LINE('Erro: ' || pvc2menssagem);\nEND;\n", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2060, -60], "id": "3877fb70-57d9-428f-a4a5-62c8b99a80c1", "name": "Cancelar NF Saída", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DFSEQ_PCLOGFATURAMENTO.nextval AS CODLOG FROM DUAL", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2240, -60], "id": "4f4d1127-bd87-49c5-a787-2ac0c0234412", "name": "PROXCODLOG", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCLOGFATURAMENTO", "mode": "list", "cachedResultName": "PCLOGFATURAMENTO"}, "valuesToSend": {"values": [{"name": "CODLOG", "value": "={{ $json[\"CODLOG\"] }}"}, {"name": "DATAHORA", "value": "SYSDATE"}, {"name": "CODFILIAL", "value": "={{ $('Loop').item.json.CODFILIAL }}"}, {"name": "PROCESSO", "value": "api/nfe/EnvEventoCancNFe"}, {"name": "CODFUNC", "value": "1"}, {"name": "DTINICIAL", "value": "SYSDATE"}, {"name": "MAQUINA", "value": "={{ $('Loop').item.json.HOST }}"}, {"name": "TERMINAL", "value": "={{ $('Loop').item.json.HOST }}"}, {"name": "OSUSER", "value": "={{ $('Loop').item.json.OS_USER }}"}, {"name": "CODIGOIDENTIFICADOR", "value": "={{ $('Loop').item.json.NUM_TRANSACAO }}"}, {"name": "LOG", "value": "Nota denegada com sucesso no Winthor."}, {"name": "TIPOLOG", "value": "INFO"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2420, -60], "id": "69d10d4a-b747-4a04-a720-da9e68338dda", "name": "PCLOGFATURAMENTO", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n  IDREG              ROWID;\n  VEST_CONT          NUMBER;\n  VEST_GER           NUMBER;\n  VNERRO             EXCEPTION;\n  XMLDATA            XMLTYPE;\n  RECORDSET          SYS_REFCURSOR;\n  G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>V        PCNFCUSTO_JSON.GUID % TYPE;\n  VCODPROD           PCMOV.CODPROD % TYPE;\n  VCODPRODANT        PCMOV.CODPROD % TYPE;\n  VQTEST             PCEST.QTEST % TYPE;\n  VQTEST_SALDO       PCEST.QTEST % TYPE;\n  VQTEST_ANT         PCEST.QTEST % TYPE;\n  VQTESTGER          PCEST.QTESTGER % TYPE;\n  VQTESTGER_SALDO    PCEST.QTESTGER % TYPE;\n  VQTESTGER_ANT      PCEST.QTESTGER % TYPE;\n  VSALDOTOTAL_CONT   PCEST.QTEST % TYPE;\n  VSALDOTOTAL_GER    PCEST.QTESTGER % TYPE;\n  VNUMTRANSENTULTENT PCEST.NUMTRANSENTULTENT % TYPE;\n  FUNCTION NUMSEQANTERIOR(PNU<PERSON>RANSENT IN PCMOV.NUMTRANSENT % TYPE,\n                          PC<PERSON><PERSON><PERSON>IAL   IN PCMOV.CODFILIAL % TYPE,\n                          PCODPROD     IN PCMOV.CODPROD % TYPE,\n                          PNUMSEQ      IN PCMOV.NUMSEQ % TYPE)\n    RETURN PCMOV.NUMSEQ % TYPE\n    IS\n      VNUMSEQ PCMOV.NUMSEQ % TYPE;\n    BEGIN\n      SELECT MAX(PCMOV.NUMSEQ)\n        INTO VNUMSEQ\n        FROM PCMOV\n        WHERE PCMOV.NUMTRANSENT = PNUMTRANSENT\n          AND DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT) > 0\n          AND SUBSTR(PCMOV.CODOPER, 1, 1) = 'E'\n          AND PCMOV.CODPROD = PCODPROD\n          AND PCMOV.CODFILIAL = PCODFILIAL\n          AND PCMOV.NUMSEQ < PNUMSEQ;\n      RETURN (VNUMSEQ);\n    END;\n  PROCEDURE CUSTOCHEIOS_ULTIMANOTA(PNUMTRANSENT PCEST.NUMTRANSENTULTENT % TYPE,\n                                   PCODFILIAL   PCEST.CODFILIAL % TYPE,\n                                   PCODPROD     PCEST.CODPROD % TYPE,\n                                   PNUMSEQ      PCMOV.NUMSEQ % TYPE,\n                                   PEST_CONT    NUMBER,\n                                   PEST_GER     NUMBER,\n                                   PQT_CHAMADA  NUMBER)\n    IS\n      VNUMSEQ PCMOV.NUMSEQ % TYPE;\n    BEGIN\n      VNUMSEQ := NUMSEQANTERIOR(PNUMTRANSENT, PCODFILIAL, PCODPROD, PNUMSEQ);\n      FOR REG IN (SELECT PCMOV.NUMTRANSENT,\n                         PCMOV.NUMSEQ,\n                         PCMOV.NUMTRANSENTULTENT,\n                         PCMOV.DTCANCEL,\n                         PCMOV.NUMTRANSENTULTENTANT,\n                         NVL(PCMOV.VLULTENTCONTSEMST, 0) VLULTENTCONTSEMST,\n                         NVL(PCMOV.BASEICMSULTENT, 0) BASEICMSULTENT,\n                         NVL(PCMOV.CUSTONFSEMST, 0) CUSTONFSEMST,\n                         NVL(PCMOV.STBCR, 0) STBCR,\n                         NVL(PCMOV.BASEBCR, 0) BASEBCR,\n                         NVL(PCMOV.VLICMSBCR, 0) VLICMSBCR,\n                         NVL(PCMOV.BASEICMSBCR, 0) BASEICMSBCR,\n                         NVL(PCMOV.ST, 0) VLSTULTENT,\n                         NVL(PCMOV.VLDESPADICIONAL, 0) VLSTGUIAULTENT,\n                         NVL(PCMOV.PERCIVA, 0) IVAULTENT,\n                         NVL(PCMOV.ALIQICMS1, 0) ALIQICMS1ULTENT,\n                         NVL(PCMOV.ALIQICMS2, 0) ALIQICMS2ULTENT,\n                         NVL(PCMOV.REDBASEIVA, 0) REDBASEIVAULTENT,\n                         NVL(PCMOV.PERCICMSFRETEFOBST, 0) PERCICMSFRETEFOBSTULTENT,\n                         NVL(PCMOV.VLFRETECONHEC, 0) VLFRETECONHECULTENT,\n                         NVL(PCMOV.PERCALIQEXTGUIA, 0) PERCALIQEXTGUIAULTENT,\n                         NVL(PCMOVCOMPLE.CUSTONFSEMSTGUIAULTENT, 0) CUSTONFSEMSTGUIAULTENT,\n                         NVL(PCMOV.VALORULTENT, 0) VALORULTENT,\n                         NVL(PCMOV.CUSTOULTENT, 0) CUSTOULTENT,\n                         NVL(PCMOVCOMPLE.CUSTOULTENTSEMST, 0) CUSTOULTENTSEMST,\n                         NVL(PCMOVCOMPLE.CUSTOULTENTLIQ, 0) CUSTOULTENTLIQ,\n                         NVL(PCMOV.CUSTOULTENTFIN, 0) CUSTOULTENTFIN,\n                         NVL(PCMOVCOMPLE.CUSTOULTENTFINSEMST, 0) CUSTOULTENTFINSEMST,\n                         NVL(PCMOVCOMPLE.CUSTOULTENTCONT, 0) CUSTOULTENTCONT,\n                         NVL(PCMOVCOMPLE.CUSTOULTENTFISCAL, 0) CUSTOULTENTFISCAL,\n                         (CASE WHEN ((PCNFENT.TIPODESCARGA = '5') OR\n                             (NVL(PCMOV.PRODBONIFICADO, 'N') = 'S')) THEN NVL(PCNFENT.CUSTOZEROCUSCONTBONIFIC, 'N') ELSE 'N' END) CUSTOZEROCUSCONTBONIFIC,\n                         (CASE WHEN ((PCNFENT.TIPODESCARGA = '5') OR\n                             (NVL(PCMOV.PRODBONIFICADO, 'N') = 'S')) THEN 1 ELSE 0 END) ENTBONIFICADO,\n                         NVL(PCMOV.PRODBONIFICADO, 'N') PRODBONIFICADO,\n                         DECODE(NVL(PCMOVCOMPLE.ALIQICMS1RET, 0), 0, NVL(PCMOV.ALIQICMS1, 0), NVL(PCMOVCOMPLE.ALIQICMS1RET, 0)) ALIQICMS1RET,\n                         DECODE(NVL(PCMOVCOMPLE.VLBCFCPSTRET, 0), 0, (NVL(PCMOVCOMPLE.VLBASEFCPST, 0) + NVL(PCMOVCOMPLE.VLBASEFCPSTGUIA, 0)), NVL(PCMOVCOMPLE.VLBCFCPSTRET, 0)) VLBCFCPSTRET,\n                         DECODE(NVL(PCMOVCOMPLE.PERFCPSTRET, 0), 0, NVL(PCMOVCOMPLE.ALIQICMSFECP, 0), NVL(PCMOVCOMPLE.PERFCPSTRET, 0)) PERFCPSTRET,\n                         DECODE(NVL(PCMOVCOMPLE.VLFCPSTRET, 0), 0, (NVL(PCMOVCOMPLE.VLFECP, 0) + NVL(PCMOVCOMPLE.VLFECPSTGUIA, 0)), NVL(PCMOVCOMPLE.VLFCPSTRET, 0)) VLFCPSTRET,\n                         NVL(PCMOVCOMPLE.VLBASEEFET, 0) VLBASEEFET,\n                         NVL(PCMOVCOMPLE.PERCREDBASEEFET, 0) PERCREDBASEEFET,\n                         NVL(PCMOVCOMPLE.PERCICMSEFET, 0) PERCICMSEFET,\n                         NVL(PCMOVCOMPLE.VLICMSEFET, 0) VLICMSEFET,\n                         NVL(PCMOVCOMPLE.VLIISUSPENSO, 0) VLIISUSPENSO,\n                         NVL(PCMOVCOMPLE.VLIPISUSPENSO, 0) VLIPISUSPENSO\n          FROM PCMOV,\n               PCMOVCOMPLE,\n               PCNFENT,\n               PCCONSUM\n          WHERE PCMOV.NUMTRANSENT = PCNFENT.NUMTRANSENT\n            AND PCMOV.NUMTRANSITEM = PCMOVCOMPLE.NUMTRANSITEM\n            AND PCMOV.NUMTRANSENT = PNUMTRANSENT\n            AND ((PCNFENT.CODCONT = NVL(PCNFENT.CODCONTFOR, PCCONSUM.CODCONTFOR))\n            OR (PCNFENT.CODCONT = PCCONSUM.CODCONTCLI)\n            OR ((PCNFENT.CODCONT = PCCONSUM.CODCONTAJUSTEEST)\n            AND (PCNFENT.TIPODESCARGA = '4'))\n            OR ((PCNFENT.TIPODESCARGA = 'S')\n            AND ((PCNFENT.ESPECIE = 'NF')\n            OR (PCNFENT.ESPECIE = 'NE'))))\n            AND ((PCNFENT.TIPODESCARGA IN ('1', '4', '5', 'S', 'A', 'R', 'J', 'N', 'I', 'B'))\n            OR (PCNFENT.TIPODESCARGA IN ('6', '8')\n            AND (NVL(PCNFENT.PCT_CUSTO, 'N') = 'S'))\n            OR (PCMOV.CODOPER = 'EG'\n            AND PCNFENT.TIPODESCARGA = 'D')\n            OR (NVL(PCNFENT.NFENTREGAFUTURA, 'N') = 'N'\n            AND PCNFENT.TIPODESCARGA = '2'))\n            AND PCMOV.CODFILIAL = PCODFILIAL\n            AND PCMOV.CODPROD = PCODPROD\n            AND PCMOV.NUMSEQ = VNUMSEQ\n            AND SUBSTR(PCMOV.CODOPER, 1, 1) = 'E'\n            AND DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT) > 0\n            AND ROWNUM = 1\n          ORDER BY PCMOV.NUMSEQ DESC)\n      LOOP\n        IF (PQT_CHAMADA <= 10)\n        THEN\n          IF ((REG.DTCANCEL IS NOT NULL)\n            AND (REG.NUMTRANSENTULTENTANT > 0))\n          THEN\n            CUSTOCHEIOS_ULTIMANOTA(REG.NUMTRANSENTULTENTANT, PCODFILIAL, PCODPROD, (CASE WHEN (REG.NUMTRANSENT = REG.NUMTRANSENTULTENTANT) THEN REG.NUMSEQ ELSE 500 END), PEST_CONT, PEST_GER, (PQT_CHAMADA + 1));\n          ELSE\n            UPDATE PCEST\n              SET PCEST.NUMTRANSENTULTENT = PNUMTRANSENT, PCEST.VLULTENTCONTSEMST = DECODE(PEST_GER, 1, DECODE(REG.VLULTENTCONTSEMST, 0, PCEST.VLULTENTCONTSEMST, REG.VLULTENTCONTSEMST), PCEST.VLULTENTCONTSEMST), PCEST.VLBCFCPSTRET = DECODE(PEST_GER, 1, DECODE(REG.VLBCFCPSTRET, 0, PCEST.VLBCFCPSTRET, REG.VLBCFCPSTRET), 2, DECODE(REG.VLBCFCPSTRET, 0, PCEST.VLBCFCPSTRET, REG.VLBCFCPSTRET), PCEST.VLBCFCPSTRET), PCEST.PERFCPSTRET = DECODE(PEST_GER, 1, DECODE(REG.PERFCPSTRET, 0, PCEST.PERFCPSTRET, REG.PERFCPSTRET), 2, DECODE(REG.PERFCPSTRET, 0, PCEST.PERFCPSTRET, REG.PERFCPSTRET), PCEST.PERFCPSTRET), PCEST.VLFCPSTRET = DECODE(PEST_GER, 1, DECODE(REG.VLFCPSTRET, 0, PCEST.VLFCPSTRET, REG.VLFCPSTRET), 2, DECODE(REG.VLFCPSTRET, 0, PCEST.VLFCPSTRET, REG.VLFCPSTRET), PCEST.VLFCPSTRET), PCEST.VLBASEEFET = DECODE(PEST_GER, 1, DECODE(REG.VLBASEEFET, 0, PCEST.VLBASEEFET, REG.VLBASEEFET), 2, DECODE(REG.VLBASEEFET, 0, PCEST.VLBASEEFET, REG.VLBASEEFET), PCEST.VLBASEEFET), PCEST.PERCREDBASEEFET = DECODE(PEST_GER, 1, DECODE(REG.PERCREDBASEEFET, 0, PCEST.PERCREDBASEEFET, REG.PERCREDBASEEFET), 2, DECODE(REG.PERCREDBASEEFET, 0, PCEST.PERCREDBASEEFET, REG.PERCREDBASEEFET), PCEST.PERCREDBASEEFET), PCEST.PERCICMSEFET = DECODE(PEST_GER, 1, DECODE(REG.PERCICMSEFET, 0, PCEST.PERCICMSEFET, REG.PERCICMSEFET), 2, DECODE(REG.PERCICMSEFET, 0, PCEST.PERCICMSEFET, REG.PERCICMSEFET), PCEST.PERCICMSEFET), PCEST.VLICMSEFET = DECODE(PEST_GER, 1, DECODE(REG.VLICMSEFET, 0, PCEST.VLICMSEFET, REG.VLICMSEFET), 2, DECODE(REG.VLICMSEFET, 0, PCEST.VLICMSEFET, REG.VLICMSEFET), PCEST.VLICMSEFET), PCEST.ALIQICMS1 = DECODE(PEST_GER, 1, DECODE(REG.ALIQICMS1RET, 0, PCEST.ALIQICMS1, REG.ALIQICMS1RET), 2, DECODE(REG.ALIQICMS1RET, 0, PCEST.ALIQICMS1, REG.ALIQICMS1RET), PCEST.ALIQICMS1), PCEST.STBCR = DECODE(PEST_GER, 1, DECODE(REG.STBCR, 0, PCEST.STBCR, REG.STBCR), 2, DECODE(REG.STBCR, 0, PCEST.STBCR, REG.STBCR), PCEST.STBCR), PCEST.BASEBCR = DECODE(PEST_GER, 1, DECODE(REG.BASEBCR, 0, PCEST.BASEBCR, REG.BASEBCR), 2, DECODE(REG.BASEBCR, 0, PCEST.BASEBCR, REG.BASEBCR), PCEST.BASEBCR), PCEST.VLICMSBCR = DECODE(PEST_GER, 1, DECODE(REG.VLICMSBCR, 0, PCEST.VLICMSBCR, REG.VLICMSBCR), 2, DECODE(REG.VLICMSBCR, 0, PCEST.VLICMSBCR, REG.VLICMSBCR), PCEST.VLICMSBCR), PCEST.BASEICMSBCR = DECODE(PEST_GER, 1, DECODE(REG.BASEICMSBCR, 0, PCEST.BASEICMSBCR, REG.BASEICMSBCR), 2, DECODE(REG.BASEICMSBCR, 0, PCEST.BASEICMSBCR, REG.BASEICMSBCR), PCEST.BASEICMSBCR), PCEST.BASEICMSULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.BASEICMSULTENT, 0, PCEST.BASEICMSULTENT, REG.BASEICMSULTENT), PCEST.BASEICMSULTENTTAB), PCEST.CUSTONFSEMSTTAB = DECODE(PEST_GER, 1, DECODE(REG.CUSTONFSEMST, 0, PCEST.CUSTONFSEMST, REG.CUSTONFSEMST), PCEST.CUSTONFSEMSTTAB), PCEST.VLSTULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.VLSTULTENT, 0, PCEST.VLSTULTENTTAB, REG.VLSTULTENT), PCEST.VLSTULTENTTAB), PCEST.VLSTGUIAULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.VLSTGUIAULTENT, 0, PCEST.VLSTGUIAULTENTTAB, REG.VLSTGUIAULTENT), PCEST.VLSTGUIAULTENTTAB), PCEST.CUSTONFSEMSTGUIAULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.CUSTONFSEMSTGUIAULTENT, 0, PCEST.CUSTONFSEMSTGUIAULTENTTAB, REG.CUSTONFSEMSTGUIAULTENT), PCEST.CUSTONFSEMSTGUIAULTENTTAB), PCEST.IVAULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.IVAULTENT, 0, PCEST.IVAULTENTTAB, REG.IVAULTENT), PCEST.IVAULTENTTAB), PCEST.ALIQICMS1ULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.ALIQICMS1ULTENT, 0, PCEST.ALIQICMS1ULTENTTAB, REG.ALIQICMS1ULTENT), PCEST.ALIQICMS1ULTENTTAB), PCEST.ALIQICMS2ULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.ALIQICMS2ULTENT, 0, PCEST.ALIQICMS2ULTENTTAB, REG.ALIQICMS2ULTENT), PCEST.ALIQICMS2ULTENTTAB), PCEST.REDBASEIVAULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.REDBASEIVAULTENT, 0, PCEST.REDBASEIVAULTENTTAB, REG.REDBASEIVAULTENT), PCEST.REDBASEIVAULTENTTAB), PCEST.PERCICMSFRETEFOBSTULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.PERCICMSFRETEFOBSTULTENT, 0, PCEST.PERCICMSFRETEFOBSTULTENTTAB, REG.PERCICMSFRETEFOBSTULTENT), PCEST.PERCICMSFRETEFOBSTULTENTTAB), PCEST.VLFRETECONHECULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.VLFRETECONHECULTENT, 0, PCEST.VLFRETECONHECULTENTTAB, REG.VLFRETECONHECULTENT), PCEST.VLFRETECONHECULTENTTAB), PCEST.PERCALIQEXTGUIAULTENTTAB = DECODE(PEST_GER, 1, DECODE(REG.PERCALIQEXTGUIAULTENT, 0, PCEST.PERCALIQEXTGUIAULTENTTAB, REG.PERCALIQEXTGUIAULTENT), PCEST.PERCALIQEXTGUIAULTENTTAB), PCEST.VALORULTENT = DECODE(PEST_GER, 1, DECODE(REG.VALORULTENT, 0, PCEST.VALORULTENT, REG.VALORULTENT), PCEST.VALORULTENT), PCEST.CUSTOULTENT = DECODE(PEST_GER, 1, DECODE(REG.CUSTOULTENT, 0, PCEST.CUSTOULTENT, REG.CUSTOULTENT), PCEST.CUSTOULTENT), PCEST.CUSTOULTENTLIQ = DECODE(PEST_GER, 1, DECODE(REG.CUSTOULTENTLIQ, 0, PCEST.CUSTOULTENTLIQ, REG.CUSTOULTENTLIQ), PCEST.CUSTOULTENTLIQ), PCEST.CUSTOULTENTSEMST = DECODE(PEST_GER, 1, DECODE(REG.CUSTOULTENTSEMST, 0, PCEST.CUSTOULTENTSEMST, REG.CUSTOULTENTSEMST), PCEST.CUSTOULTENTSEMST), PCEST.CUSTOULTENTFIN = DECODE(PEST_GER, 1, DECODE(REG.CUSTOULTENTFIN, 0, PCEST.CUSTOULTENTFIN, REG.CUSTOULTENTFIN), PCEST.CUSTOULTENTFIN), PCEST.CUSTOULTENTFINSEMST = DECODE(PEST_GER, 1, DECODE(REG.CUSTOULTENTFINSEMST, 0, PCEST.CUSTOULTENTFINSEMST, REG.CUSTOULTENTFINSEMST), PCEST.CUSTOULTENTFINSEMST), PCEST.CUSTOULTENTCONT = DECODE(PEST_CONT, 1, DECODE(REG.CUSTOULTENTCONT, 0, (CASE WHEN ((REG.CUSTOZEROCUSCONTBONIFIC = 'S') AND ((REG.ENTBONIFICADO = 1) OR (REG.PRODBONIFICADO = 'S'))) THEN REG.CUSTOULTENTCONT ELSE PCEST.CUSTOULTENTCONT END), REG.CUSTOULTENTCONT), PCEST.CUSTOULTENTCONT), PCEST.CUSTOULTENTFISCAL = DECODE(PEST_CONT, 1, DECODE(REG.CUSTOULTENTFISCAL, 0, (CASE WHEN ((REG.CUSTOZEROCUSCONTBONIFIC = 'S') AND ((REG.ENTBONIFICADO = 1) OR (REG.PRODBONIFICADO = 'S'))) THEN REG.CUSTOULTENTFISCAL ELSE PCEST.CUSTOULTENTFISCAL END), REG.CUSTOULTENTFISCAL), PCEST.CUSTOULTENTFISCAL), PCEST.VLIISUSPENSOULTENT = REG.VLIISUSPENSO, PCEST.VLIPISUSPENSOULTENT = REG.VLIPISUSPENSO\n              WHERE PCEST.CODFILIAL = PCODFILIAL\n              AND PCEST.CODPROD = PCODPROD;\n          END IF;\n        END IF;\n      END LOOP;\n    END;\n  PROCEDURE HIST_CUSTO_ANT(PLINHA     PCMOVCOMPLE.NUMTRANSITEM % TYPE,\n                           PCODFILIAL PCEST.CODFILIAL % TYPE,\n                           PCODPROD   PCEST.CODPROD % TYPE)\n    IS\n    BEGIN\n      UPDATE PCMOVCOMPLE MC\n        SET (MC.CUSTOREPANTCANC, MC.CUSTOREALANTCANC, MC.CUSTOREALSEMSTANTCANC, MC.CUSTOFINANTCANC, MC.CUSTOFINSEMSTANTCANC, MC.CUSTOFISCALANTCANC, MC.CUSTOCONTANTCANC) = (SELECT E.CUSTOREP, E.CUSTOREAL, E.CUSTOREALSEMST, E.CUSTOFIN, E.CUSTOFINSEMST, E.CUSTOFISCAL, E.CUSTOCONT FROM PCEST E WHERE E.CODFILIAL = PCODFILIAL AND E.CODPROD = PCODPROD)\n        WHERE MC.NUMTRANSITEM = PLINHA;\n    END;\n  PROCEDURE HIST_CUSTO_APOS(PLINHA     PCMOVCOMPLE.NUMTRANSITEM % TYPE,\n                            PCODFILIAL PCEST.CODFILIAL % TYPE,\n                            PCODPROD   PCEST.CODPROD % TYPE)\n    IS\n    BEGIN\n      UPDATE PCMOVCOMPLE MC\n        SET (MC.CUSTOREPCANC, MC.CUSTOREALCANC, MC.CUSTOREALSEMSTCANC, MC.CUSTOFINCANC, MC.CUSTOFINSEMSTCANC, MC.CUSTOFISCALCANC, MC.CUSTOCONTCANC) = (SELECT E.CUSTOREP, E.CUSTOREAL, E.CUSTOREALSEMST, E.CUSTOFIN, E.CUSTOFINSEMST, E.CUSTOFISCAL, E.CUSTOCONT FROM PCEST E WHERE E.CODFILIAL = PCODFILIAL AND E.CODPROD = PCODPROD)\n        WHERE MC.NUMTRANSITEM = PLINHA;\n    END;\n  PROCEDURE ATUALIZA_CUSTOULTENT_DEVCLI(PNUMTRANSENT PCMOV.NUMTRANSENT % TYPE,\n                                        PCODFILIAL   PCEST.CODFILIAL % TYPE,\n                                        PCODPROD     PCEST.CODPROD % TYPE,\n                                        PNUMSEQ      PCMOV.NUMSEQ % TYPE)\n    IS\n      VNVALORULTENT         PCMOV.VALORULTENT % TYPE;\n      VNCUSTOULTENT         PCMOV.CUSTOULTENT % TYPE;\n      VNCUSTOULTENTFIN      PCMOV.CUSTOULTENTFIN % TYPE;\n      VNCUSTOULTENTCONT     PCMOVCOMPLE.CUSTOULTENTCONT % TYPE;\n      VNCUSTOULTENTSEMST    PCMOVCOMPLE.CUSTOULTENTSEMST % TYPE;\n      VNCUSTOULTENTFINSEMST PCMOVCOMPLE.CUSTOULTENTFINSEMST % TYPE;\n      VNCUSTOULTENTLIQ      PCMOVCOMPLE.CUSTOULTENTLIQ % TYPE;\n      VNCUSTOULTENTFISCAL   PCMOVCOMPLE.CUSTOULTENTFISCAL % TYPE;\n      VNNUMTRANSITEM        PCMOV.NUMTRANSITEM % TYPE;\n    BEGIN\n      SELECT PCMOV.VALORULTENT,\n             PCMOV.CUSTOULTENT,\n             PCMOV.CUSTOULTENTFIN,\n             PCMOVCOMPLE.CUSTOULTENTCONT,\n             PCMOVCOMPLE.CUSTOULTENTSEMST,\n             PCMOVCOMPLE.CUSTOULTENTFINSEMST,\n             PCMOVCOMPLE.CUSTOULTENTLIQ,\n             PCMOVCOMPLE.CUSTOULTENTFISCAL\n        INTO VNVALORULTENT,\n             VNCUSTOULTENT,\n             VNCUSTOULTENTFIN,\n             VNCUSTOULTENTCONT,\n             VNCUSTOULTENTSEMST,\n             VNCUSTOULTENTFINSEMST,\n             VNCUSTOULTENTLIQ,\n             VNCUSTOULTENTFISCAL\n        FROM PCMOV,\n             PCMOVCOMPLE\n        WHERE PCMOV.NUMTRANSITEM = PCMOVCOMPLE.NUMTRANSITEM\n          AND PCMOV.CODPROD = PCODPROD\n          AND PCMOV.NUMTRANSENT = PNUMTRANSENT\n          AND PCMOV.NUMSEQ = PNUMSEQ\n          AND PCMOV.CODFILIAL = PCODFILIAL\n          AND GREATEST(NVL(PCMOV.QT, 0), NVL(PCMOV.QTCONT, 0)) > 0;\n      SELECT NUMTRANSITEM\n        INTO VNNUMTRANSITEM\n        FROM PCMOV\n        WHERE PCMOV.NUMTRANSENT = PNUMTRANSENT\n          AND PCMOV.CODFILIAL = PCODFILIAL\n          AND PCMOV.CODPROD = PCODPROD\n          AND PCMOV.NUMSEQ = PNUMSEQ\n          AND DECODE(NVL(PCMOV.QT, 0), 0, NVL(PCMOV.QTCONT, 0), NVL(PCMOV.QT, 0)) < 0;\n      UPDATE PCMOV\n        SET VALORULTENT = CASE WHEN NVL(VALORULTENT, 0) = 0 THEN VNVALORULTENT ELSE NVL(VALORULTENT, 0) END, CUSTOULTENT = CASE WHEN NVL(CUSTOULTENT, 0) = 0 THEN VNCUSTOULTENT ELSE NVL(CUSTOULTENT, 0) END, CUSTOULTENTFIN = CASE WHEN NVL(CUSTOULTENTFIN, 0) = 0 THEN VNCUSTOULTENTFIN ELSE NVL(CUSTOULTENTFIN, 0) END\n        WHERE NUMTRANSITEM = VNNUMTRANSITEM;\n      UPDATE PCMOVCOMPLE\n        SET CUSTOULTENTLIQ = CASE WHEN NVL(CUSTOULTENTLIQ, 0) = 0 THEN VNCUSTOULTENTLIQ ELSE NVL(CUSTOULTENTLIQ, 0) END, CUSTOULTENTSEMST = CASE WHEN NVL(CUSTOULTENTSEMST, 0) = 0 THEN VNCUSTOULTENTSEMST ELSE NVL(CUSTOULTENTSEMST, 0) END, CUSTOULTENTFINSEMST = CASE WHEN NVL(CUSTOULTENTFINSEMST, 0) = 0 THEN VNCUSTOULTENTFINSEMST ELSE NVL(CUSTOULTENTFINSEMST, 0) END, CUSTOULTENTCONT = CASE WHEN NVL(CUSTOULTENTCONT, 0) = 0 THEN VNCUSTOULTENTCONT ELSE NVL(CUSTOULTENTCONT, 0) END, CUSTOULTENTFISCAL = CASE WHEN NVL(CUSTOULTENTFISCAL, 0) = 0 THEN VNCUSTOULTENTFISCAL ELSE NVL(CUSTOULTENTFISCAL, 0) END\n        WHERE NUMTRANSITEM = VNNUMTRANSITEM;\n    END;\nBEGIN\n  VCODPRODANT := 0;\n  FOR REGISTROS IN (SELECT PCMOV.NUMTRANSENT,\n                           PCNFENT.TIPODESCARGA,\n                           PCMOV.CODPROD,\n                           PCMOV.CODFILIAL,\n                           DECODE(1307, 1436, 'AB', PCMOV.STATUS) STATUS,\n                           PCMOV.CODOPER,\n                           PCMOV.NUMSEQ,\n                           PCMOV.DTMOVLOG,\n                           PCMOV.NUMTRANSENTULTENTANT,\n                           DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT) QT,\n                           NVL(PCNFENT.ALTCUSTOENTREMESSA, 'N') ALTCUSTOENTREMESSA,\n                           PCMOV.DTULTENTANT,\n                           NVL(PCMOV.VALORULTENT, 0) VALORULTENT,\n                           NVL(PCMOV.CUSTOREP, 0) CUSTOREP,\n                           NVL(PCMOV.CUSTOULTENT, 0) CUSTOULTENT,\n                           NVL(PCMOV.CUSTOREAL, 0) CUSTOREAL,\n                           NVL(PCMOVCOMPLE.CUSTOULTENTSEMST, 0) CUSTOULTENTSEMST,\n                           NVL(PCMOV.CUSTOREALSEMST, 0) CUSTOREALSEMST,\n                           NVL(PCMOV.CUSTOULTENTFIN, 0) CUSTOULTENTFIN,\n                           NVL(PCMOV.CUSTOFIN, 0) CUSTOFIN,\n                           NVL(PCMOVCOMPLE.CUSTOULTENTFINSEMST, 0) CUSTOULTENTFINSEMST,\n                           NVL(PCMOVCOMPLE.CUSTOFINSEMST, 0) CUSTOFINSEMST,\n                           NVL(PCMOVCOMPLE.CUSTOREALLIQ, 0) CUSTOREALLIQ,\n                           NVL(PCMOVCOMPLE.CUSTOULTENTLIQ, 0) CUSTOULTENTLIQ,\n                           NVL(PCMOVCOMPLE.CUSTOULTENTCONT, 0) CUSTOULTENTCONT,\n                           NVL(PCMOV.CUSTOCONT, 0) CUSTOCONT,\n                           NVL(PCMOVCOMPLE.CUSTOULTENTFISCAL, 0) CUSTOULTENTFISCAL,\n                           NVL(PCMOVCOMPLE.CUSTOFISCAL, 0) CUSTOFISCAL,\n                           (CASE WHEN (PCNFENT.TIPODESCARGA IN ('N', 'I')) THEN 0 ELSE NVL(PCNFENT.PERCACRESCIMOCUSTOREAL, 0) END) PERCACRESCIMOCUSTOREAL,\n                           (CASE WHEN (PCNFENT.TIPODESCARGA IN ('N', 'I')) THEN 0 ELSE NVL(PCNFENT.PERCACRESCIMOCUSTOFIN, 0) END) PERCACRESCIMOCUSTOFIN,\n                           (CASE WHEN ((PCNFENT.TIPODESCARGA <> 'I') OR\n                               ((PCNFENT.TIPODESCARGA = 'I') AND\n                               ((NVL(PCMOV.VLFRETECONHEC, 0)\n                               + (CASE WHEN (NVL(PARAMFILIAL.OBTERCOMOVARCHAR2('GERARNFCOMPLEOUTROSCUSTOS', PCMOV.CODFILIAL), 'N') = 'S') THEN NVL(PCMOVCOMPLE.VLOUTROSCUSTOSCUSTOCONT, 0) ELSE 0 END)\n                               + NVL(PCMOVCOMPLE.VLNFSERVICO, 0)) > 0))) THEN 1 ELSE 0 END) ESTORNACONTABIL,\n                           PCEST.NUMTRANSENTULTENT,\n                           PCMOVCOMPLE.NUMTRANSITEM LINHA,\n                           PCMOV.ROWID LINHAX,\n                           NVL((SELECT MAX(PCNFSAID.CONDVENDA)\n                               FROM PCNFSAID,\n                                    PCESTCOM\n                               WHERE PCNFSAID.NUMTRANSVENDA = PCESTCOM.NUMTRANSVENDA\n                                 AND PCESTCOM.NUMTRANSENT = PCNFENT.NUMTRANSENT\n                                 AND PCNFSAID.CONDVENDA IN (1, 4, 5, 8, 9, 10, 11, 13, 20)), 0) CONDVENDA,\n                           PCMOVCOMPLE.VLIISUSPENSOULTENT_ANT,\n                           PCMOVCOMPLE.VLIISUSPENSO,\n                           PCMOVCOMPLE.VLIPISUSPENSOULTENT_ANT,\n                           PCMOVCOMPLE.VLIPISUSPENSO\n      FROM PCMOV,\n           PCMOVCOMPLE,\n           PCEST,\n           PCNFENT,\n           PCCONSUM\n      WHERE PCMOV.NUMTRANSENT = PCNFENT.NUMTRANSENT\n        AND PCMOV.NUMTRANSITEM = PCMOVCOMPLE.NUMTRANSITEM\n        AND PCMOV.CODFILIAL = PCEST.CODFILIAL\n        AND PCMOV.CODPROD = PCEST.CODPROD\n        AND PCMOV.NUMTRANSENT = {{ $json.NUM_TRANSACAO }}\n        AND ((PCNFENT.CODCONT = NVL(PCNFENT.CODCONTFOR, PCCONSUM.CODCONTFOR))\n        OR (PCNFENT.CODCONT = PCCONSUM.CODCONTCLI)\n        OR ((PCNFENT.CODCONT = PCCONSUM.CODCONTAJUSTEEST)\n        AND (PCNFENT.TIPODESCARGA = '4'))\n        OR ((PCNFENT.TIPODESCARGA = 'S')\n        AND ((PCNFENT.ESPECIE = 'NF')\n        OR (PCNFENT.ESPECIE = 'NE')))\n        OR (PCNFENT.CODCONT = PCCONSUM.CODCONTDEVCLI))\n        AND ((PCNFENT.TIPODESCARGA IN ('1', '4', '5', 'S', 'A', 'R', 'J', 'N', 'I'))\n        OR (PCNFENT.TIPODESCARGA IN ('6', '8')\n        AND (NVL(PCNFENT.PCT_CUSTO, 'N') = 'S'))\n        OR (PCMOV.CODOPER = 'EG'\n        AND PCNFENT.TIPODESCARGA = 'D')\n        OR (PCMOV.CODOPER <> 'EV'\n        AND PCNFENT.TIPODESCARGA = 'B')\n        OR (NVL(PCNFENT.NFENTREGAFUTURA, 'N') = 'N'\n        AND PCNFENT.TIPODESCARGA = '2'))\n        AND PCMOV.CODOPER <> 'EI'\n        AND SUBSTR(PCMOV.CODOPER, 1, 1) = 'E'\n        AND PCMOV.STATUS IN ('A', 'B', 'AB')\n        AND DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT) > 0\n      ORDER BY PCMOV.CODPROD,\n               PCMOV.NUMSEQ DESC)\n  LOOP\n    IDREG := REGISTROS.LINHAX;\n    VCODPROD := REGISTROS.CODPROD;\n    VEST_CONT := 0;\n    VEST_GER := 0;\n    IF ((REGISTROS.CUSTOULTENTCONT < 0)\n      AND (REGISTROS.ESTORNACONTABIL = 1))\n    THEN\n      RAISE VNERRO;\n    END IF;\n    SELECT PCEST.NUMTRANSENTULTENT,\n           PCEST.QTEST,\n           PCEST.QTESTGER\n      INTO VNUMTRANSENTULTENT,\n           VQTEST_SALDO,\n           VQTESTGER_SALDO\n      FROM PCEST\n      WHERE PCEST.CODFILIAL = REGISTROS.CODFILIAL\n        AND PCEST.CODPROD = REGISTROS.CODPROD;\n    IF (VCODPRODANT = VCODPROD)\n    THEN\n      VQTEST := VQTEST - REGISTROS.QT;\n      VQTESTGER := VQTESTGER - REGISTROS.QT;\n      VQTEST_ANT := VSALDOTOTAL_CONT;\n      VQTESTGER_ANT := VSALDOTOTAL_GER;\n      VSALDOTOTAL_CONT := VSALDOTOTAL_CONT - REGISTROS.QT;\n      VSALDOTOTAL_GER := VSALDOTOTAL_GER - REGISTROS.QT;\n    ELSIF (VCODPRODANT <> VCODPROD)\n    THEN\n      VCODPRODANT := VCODPROD;\n      VQTEST := VQTEST_SALDO;\n      VQTESTGER := VQTESTGER_SALDO;\n      VQTEST_ANT := VQTEST_SALDO;\n      VQTESTGER_ANT := VQTESTGER_SALDO;\n      VSALDOTOTAL_CONT := VQTEST - REGISTROS.QT;\n      VSALDOTOTAL_GER := VQTESTGER - REGISTROS.QT;\n    END IF;\n    HIST_CUSTO_ANT(REGISTROS.LINHA, REGISTROS.CODFILIAL, REGISTROS.CODPROD);\n    OPEN RECORDSET FOR\n    SELECT REGISTROS.NUMTRANSENT AS NUMTRANSENT,\n           REGISTROS.CODPROD AS CODPROD,\n           REGISTROS.CODFILIAL AS CODFILIAL,\n           REGISTROS.NUMSEQ AS NUMSEQ,\n           REGISTROS.QT AS QT,\n           REGISTROS.VALORULTENT AS VALORULTENT_MOV,\n           REGISTROS.CUSTOREP AS CUSTOREP_MOV,\n           REGISTROS.CUSTOULTENT AS CUSTOULTENT_MOV,\n           REGISTROS.CUSTOREAL AS CUSTOREAL_MOV,\n           REGISTROS.CUSTOULTENTSEMST AS CUSTOULTENTSEMST_MOV,\n           REGISTROS.CUSTOREALSEMST AS CUSTOREALSEMST_MOV,\n           REGISTROS.CUSTOULTENTFIN AS CUSTOULTENTFIN_MOV,\n           REGISTROS.CUSTOFIN AS CUSTOFIN_MOV,\n           REGISTROS.CUSTOULTENTFINSEMST AS CUSTOULTENTFINSEMST_MOV,\n           REGISTROS.CUSTOFINSEMST AS CUSTOFINSEMST_MOV,\n           REGISTROS.CUSTOREALLIQ AS CUSTOREALLIQ_MOV,\n           REGISTROS.CUSTOULTENTLIQ AS CUSTOULTENTLIQ_MOV,\n           REGISTROS.CUSTOULTENTCONT AS CUSTOULTENTCONT_MOV,\n           REGISTROS.CUSTOCONT AS CUSTOCONT_MOV,\n           PCEST.CUSTOREP AS CUSTOREP_ESTANT,\n           PCEST.CUSTOREALLIQ AS CUSTOREALLIQ_ESTANT,\n           PCEST.CUSTOREAL AS CUSTOREAL_ESTANT,\n           PCEST.CUSTOREALSEMST AS CUSTOREALSEMST_ESTANT,\n           PCEST.CUSTOFIN AS CUSTOFIN_ESTANT,\n           PCEST.CUSTOFINSEMST AS CUSTOFINSEMST_ESTANT,\n           PCEST.CUSTOCONT AS CUSTOCONT_ESTANT,\n           VQTEST AS VQTEST,\n           VQTESTGER AS VQTESTGER,\n           VQTEST_ANT AS VQTEST_ANT,\n           VQTESTGER_ANT AS VQTESTGER_ANT,\n           VSALDOTOTAL_CONT AS VSALDOTOTAL_CONT,\n           VSALDOTOTAL_GER AS VSALDOTOTAL_GER,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREP * VQTESTGER_ANT) - (REGISTROS.VALORULTENT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREP END) = 0) THEN PCEST.CUSTOREP ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREP * VQTESTGER_ANT) - (REGISTROS.VALORULTENT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREP END) END) AS CUSTOREP,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALLIQ * VQTESTGER_ANT) - (REGISTROS.CUSTOULTENTLIQ * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALLIQ END) = 0) THEN PCEST.CUSTOREALLIQ ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALLIQ * VQTESTGER_ANT) - (REGISTROS.CUSTOULTENTLIQ * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALLIQ END) END) AS CUSTOREALLIQ,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREAL * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENT * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREAL END) = 0) THEN PCEST.CUSTOREAL ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREAL * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENT * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREAL END) END) AS CUSTOREAL,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALSEMST END) = 0) THEN PCEST.CUSTOREALSEMST ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALSEMST END) END) AS CUSTOREALSEMST,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFIN * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFIN * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFIN END) = 0) THEN PCEST.CUSTOFIN ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFIN * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFIN * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFIN END) END) AS CUSTOFIN,\n           (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFINSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFINSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFINSEMST END) = 0) THEN PCEST.CUSTOFINSEMST ELSE (CASE WHEN ((VQTESTGER >= 0) AND\n                   (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFINSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFINSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFINSEMST END) END) AS CUSTOFINSEMST,\n           (CASE WHEN (REGISTROS.TIPODESCARGA = 'I') THEN (CASE WHEN (REGISTROS.ESTORNACONTABIL = 1) THEN (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND\n                               (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) = 0) THEN PCEST.CUSTOCONT ELSE (CASE WHEN ((VQTEST >= 0) AND\n                               (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) END) ELSE PCEST.CUSTOCONT END) ELSE (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND\n                       (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) = 0) THEN PCEST.CUSTOCONT ELSE (CASE WHEN ((VQTEST >= 0) AND\n                       (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) END) END) AS CUSTOCONT,\n           REGISTROS.PERCACRESCIMOCUSTOREAL AS PERCACRESCIMOCUSTOREAL,\n           REGISTROS.PERCACRESCIMOCUSTOFIN AS PERCACRESCIMOCUSTOFIN,\n           REGISTROS.ALTCUSTOENTREMESSA AS ALTCUSTOENTREMESSA,\n           REGISTROS.ESTORNACONTABIL AS ESTORNACONTABIL,\n           REGISTROS.CONDVENDA AS CONDVENDA,\n           REGISTROS.NUMTRANSENTULTENTANT AS NUMTRANSENTULTENTANT,\n           PCEST.NUMTRANSENTULTENT AS NUMTRANSENTULTENT,\n           PCEST.VLIPISUSPENSOULTENT AS VLIPISUSPENSOULTENT,\n           PCEST.VLIISUSPENSOULTENT AS VLIISUSPENSOULTENT\n      FROM PCEST\n      WHERE PCEST.CODFILIAL = REGISTROS.CODFILIAL\n        AND PCEST.CODPROD = REGISTROS.CODPROD;\n    XMLDATA := XMLTYPE(RECORDSET);\n    SELECT SYS_GUID()\n      INTO GUIDJSONDEV\n      FROM DUAL;\n    UPDATE PCMOVCOMPLE\n      SET GUID_JSON_DEV = GUIDJSONDEV\n      WHERE NUMTRANSITEM = REGISTROS.LINHA;\n    INSERT INTO PCNFCUSTO_JSON (\n      GUID, JSON\n    )\n    VALUES (GUIDJSONDEV, XMLDATA.GETCLOBVAL());\n    SAVEPOINT IDREG;\n    IF ((REGISTROS.TIPODESCARGA IN ('2', '4', '5', 'N'))\n      OR ((REGISTROS.TIPODESCARGA = '8')\n      AND (REGISTROS.CONDVENDA = 4))\n      OR ((REGISTROS.TIPODESCARGA = 'R')\n      AND (REGISTROS.ALTCUSTOENTREMESSA = 'N')))\n    THEN\n      VEST_CONT := 1;\n      UPDATE PCEST\n        SET PCEST.DTULTENT = PCEST.DTULTENT, PCEST.CUSTOCONT = (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) = 0) THEN PCEST.CUSTOCONT ELSE (CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) END), PCEST.CUSTOFISCAL = (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) = 0) THEN PCEST.CUSTOFISCAL ELSE (CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) END)\n        WHERE PCEST.CODFILIAL = REGISTROS.CODFILIAL\n        AND PCEST.CODPROD = REGISTROS.CODPROD;\n    END IF;\n    IF (REGISTROS.TIPODESCARGA IN ('1', 'S', 'A', 'J', 'I', 'B', 'D')\n      OR ((REGISTROS.TIPODESCARGA = '6')\n      AND (REGISTROS.CONDVENDA IN (1, 5, 8, 9, 10, 11, 13, 20)))\n      OR ((REGISTROS.TIPODESCARGA = 'R')\n      AND (REGISTROS.ALTCUSTOENTREMESSA = 'S')))\n    THEN\n      VEST_GER := 1;\n      VEST_CONT := (CASE WHEN ((REGISTROS.ESTORNACONTABIL = 1) AND\n          (REGISTROS.TIPODESCARGA = 'I')) THEN 1 ELSE 0 END);\n      UPDATE PCEST\n        SET PCEST.DTULTENT = DECODE(PCEST.NUMTRANSENTULTENT, REGISTROS.NUMTRANSENT, REGISTROS.DTULTENTANT, PCEST.DTULTENT), PCEST.CUSTOREP = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREP * VQTESTGER_ANT) - (REGISTROS.VALORULTENT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREP END) = 0) THEN PCEST.CUSTOREP ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREP * VQTESTGER_ANT) - (REGISTROS.VALORULTENT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREP END) END), PCEST.CUSTOREALLIQ = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALLIQ * VQTESTGER_ANT) - (REGISTROS.CUSTOULTENTLIQ * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALLIQ END) = 0) THEN PCEST.CUSTOREALLIQ ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALLIQ * VQTESTGER_ANT) - (REGISTROS.CUSTOULTENTLIQ * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALLIQ END) END), PCEST.CUSTOREAL = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREAL * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENT * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREAL END) = 0) THEN PCEST.CUSTOREAL ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREAL * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENT * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREAL END) END), PCEST.CUSTOREALSEMST = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALSEMST END) = 0) THEN PCEST.CUSTOREALSEMST ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOREALSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOREAL / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOREALSEMST END) END), PCEST.CUSTOFIN = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFIN * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFIN * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFIN END) = 0) THEN PCEST.CUSTOFIN ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFIN * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFIN * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFIN END) END), PCEST.CUSTOFINSEMST = (CASE WHEN ((CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFINSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFINSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFINSEMST END) = 0) THEN PCEST.CUSTOFINSEMST ELSE (CASE WHEN ((VQTESTGER >= 0) AND (VSALDOTOTAL_GER >= 0)) THEN ROUND((((PCEST.CUSTOFINSEMST * VQTESTGER_ANT) - (ROUND((REGISTROS.CUSTOULTENTFINSEMST * (1 + (REGISTROS.PERCACRESCIMOCUSTOFIN / 100))), 6) * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_GER <> 0) THEN (VSALDOTOTAL_GER) ELSE 1 END)), 6) ELSE PCEST.CUSTOFINSEMST END) END), PCEST.CUSTOCONT = (CASE WHEN (REGISTROS.TIPODESCARGA = 'I') THEN (CASE WHEN (REGISTROS.ESTORNACONTABIL = 1) THEN (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) = 0) THEN PCEST.CUSTOCONT ELSE (CASE WHEN ((VQTEST >= 0) AND (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) END) ELSE PCEST.CUSTOCONT END) ELSE (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) = 0) THEN PCEST.CUSTOCONT ELSE (CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOCONT * VQTEST_ANT) - (REGISTROS.CUSTOULTENTCONT * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOCONT END) END) END), PCEST.CUSTOFISCAL = (CASE WHEN (REGISTROS.TIPODESCARGA = 'I') THEN (CASE WHEN (REGISTROS.ESTORNACONTABIL = 1) THEN (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) = 0) THEN PCEST.CUSTOFISCAL ELSE (CASE WHEN ((VQTEST >= 0) AND (REGISTROS.QT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VQTEST_ANT <> 0) THEN VQTEST_ANT ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) END) ELSE PCEST.CUSTOFISCAL END) ELSE (CASE WHEN ((CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) = 0) THEN PCEST.CUSTOFISCAL ELSE (CASE WHEN ((VQTEST >= 0) AND (VSALDOTOTAL_CONT >= 0)) THEN ROUND((((PCEST.CUSTOFISCAL * VQTEST_ANT) - (REGISTROS.CUSTOULTENTFISCAL * REGISTROS.QT)) / (CASE WHEN (VSALDOTOTAL_CONT <> 0) THEN (VSALDOTOTAL_CONT) ELSE 1 END)), 6) ELSE PCEST.CUSTOFISCAL END) END) END)\n        WHERE PCEST.CODFILIAL = REGISTROS.CODFILIAL\n        AND PCEST.CODPROD = REGISTROS.CODPROD;\n    END IF;\n    FOR CUSTOOK IN (SELECT (CASE WHEN NVL(PCEST.CUSTOREAL, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOREP, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOFIN, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOCONT, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOREALSEMST, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOFINSEMST, 0) < 0 THEN 1 WHEN NVL(PCEST.CUSTOREALLIQ, 0) < 0 THEN 1 ELSE 0 END) CUSTONEGATIVO\n        FROM PCEST\n        WHERE PCEST.CODPROD = REGISTROS.CODPROD\n          AND PCEST.CODFILIAL = REGISTROS.CODFILIAL)\n    LOOP\n      IF (CUSTOOK.CUSTONEGATIVO > 0)\n      THEN\n        VCODPROD := REGISTROS.CODPROD;\n        ROLLBACK TO IDREG;\n        BEGIN\n          FOR ATUAIS IN (SELECT PCEST.CUSTOREAL,\n                                PCEST.CUSTOREP,\n                                PCEST.CUSTOFIN,\n                                PCEST.CUSTOREALSEMST,\n                                PCEST.CUSTOCONT,\n                                PCEST.CUSTOFINSEMST,\n                                PCEST.CUSTOREALLIQ\n              FROM PCEST\n              WHERE PCEST.CODPROD = REGISTROS.CODPROD\n                AND PCEST.CODFILIAL = REGISTROS.CODFILIAL)\n          LOOP\n            UPDATE PCMOV\n              SET PCMOV.CUSTOULTENT = ATUAIS.CUSTOREAL, PCMOV.VALORULTENT = ATUAIS.CUSTOREP, PCMOV.CUSTOULTENTFIN = ATUAIS.CUSTOFIN, PCMOV.CUSTOREAL = ATUAIS.CUSTOREAL, PCMOV.CUSTOREP = ATUAIS.CUSTOREP, PCMOV.CUSTOFIN = ATUAIS.CUSTOFIN, PCMOV.CUSTOCONT = ATUAIS.CUSTOCONT, PCMOV.CUSTOREALSEMST = ATUAIS.CUSTOREALSEMST\n              WHERE PCMOV.NUMTRANSITEM = REGISTROS.LINHA;\n            UPDATE PCMOVCOMPLE\n              SET PCMOVCOMPLE.CUSTOULTENTSEMST = ATUAIS.CUSTOREALSEMST, PCMOVCOMPLE.CUSTOULTENTCONT = ATUAIS.CUSTOCONT, PCMOVCOMPLE.CUSTOULTENTFINSEMST = ATUAIS.CUSTOFINSEMST, PCMOVCOMPLE.CUSTOULTENTLIQ = ATUAIS.CUSTOREALLIQ, PCMOVCOMPLE.CUSTOFINSEMST = ATUAIS.CUSTOFINSEMST, PCMOVCOMPLE.CUSTOREALLIQ = ATUAIS.CUSTOREALLIQ\n              WHERE PCMOVCOMPLE.NUMTRANSITEM = REGISTROS.LINHA;\n          END LOOP;\n        END;\n      END IF;\n    END LOOP;\n    HIST_CUSTO_APOS(REGISTROS.LINHA, REGISTROS.CODFILIAL, REGISTROS.CODPROD);\n    IF REGISTROS.TIPODESCARGA IN ('6', '8')\n    THEN\n      ATUALIZA_CUSTOULTENT_DEVCLI(REGISTROS.NUMTRANSENT, REGISTROS.CODFILIAL, REGISTROS.CODPROD, REGISTROS.NUMSEQ);\n    END IF;\n    IF ((REGISTROS.TIPODESCARGA = 'R')\n      AND (REGISTROS.ALTCUSTOENTREMESSA = 'N')\n      AND (VEST_GER = 0))\n    THEN\n      VEST_GER := 2;\n    END IF;\n    IF (((REGISTROS.NUMTRANSENT = VNUMTRANSENTULTENT)\n      OR (REGISTROS.NUMTRANSENTULTENTANT = VNUMTRANSENTULTENT))\n      AND (REGISTROS.NUMTRANSENTULTENTANT > 0)\n      AND ((VEST_CONT = 1)\n      OR (VEST_GER = 1)\n      OR (VEST_GER = 2)))\n    THEN\n      CUSTOCHEIOS_ULTIMANOTA(REGISTROS.NUMTRANSENTULTENTANT, REGISTROS.CODFILIAL, REGISTROS.CODPROD, (CASE WHEN (REGISTROS.NUMTRANSENT = REGISTROS.NUMTRANSENTULTENTANT) THEN REGISTROS.NUMSEQ ELSE 500 END), VEST_CONT, VEST_GER, 0);\n    END IF;\n  END LOOP;\nEXCEPTION\n  WHEN VNERRO THEN RAISE_APPLICATION_ERROR(-20051, 'O custo da última entrada contábil (PCMOVCOMPLE.CUSTOULTENTCONT) deve ser igual ou maior que zero. Produto: ' || TO_CHAR(VCODPROD));\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2060, 160], "id": "5f5ba42f-5e2a-4662-a91a-629ee6bfd93e", "name": "Cancelar NF Entrada", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFCAN", "mode": "list", "cachedResultName": "PCNFCAN"}, "valuesToSend": {"values": [{"name": "CODFILIAL", "value": "={{ $('Loop1').item.json.CODFILIAL }}"}, {"name": "NUMTRANSENT", "value": "={{ $('Loop1').item.json.NUM_TRANSACAO }}"}, {"name": "NUMTRANSVENDA", "value": "0"}, {"name": "CODFUNCEMITE", "value": "1"}, {"name": "DATAEMISSAO", "value": "={{ $('Loop1').item.json.DATA }}"}, {"name": "CODFUNCCANC", "value": "1"}, {"name": "DATACANC", "value": "={{ $now.toFormat('dd/MM/yyyy') }}"}, {"name": "VLTOTAL", "value": "={{ $('Loop1').item.json.VLTOTAL }}"}, {"name": "CODCLI", "value": "0"}, {"name": "CODFORNEC", "value": "={{ $('Loop1').item.json.CODPARCEIRO }}"}, {"name": "MOTIVO", "value": "NF CANCELADA NO MERCADO LIVRE"}, {"name": "NUMPED", "value": "0"}, {"name": "NUMCAR", "value": "0"}, {"name": "NUMPEDCOMPRA", "value": "0"}, {"name": "CODROTINA", "value": "1307"}, {"name": "DESCRICAO", "value": "=Fornecedor: {{ $('Loop1').item.json.PARCEIRO }} / CONF.NF: {{ $('Loop1').item.json.NUMNOTA }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2240, 160], "id": "39822491-0658-473c-ab74-2f869679ba8b", "name": "PCNFCAN", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n  VNUMTRANSITEMOLD PCMOV.NUMTRANSITEM % TYPE;\n  VNUMTRA<PERSON>ITEMNEW PCMOV.NUMTRANSITEM % TYPE;\n  VQTENTREGUE      PCITEM.QTENTREGUE % TYPE;\n  V_NUMTRANSENT    NUMBER := {{ $('Loop1').item.json.NUM_TRANSACAO }};\n  V_NUMNOTA        NUMBER := {{ $('Loop1').item.json.NUMNOTA }};\n  V_CODFUNC        NUMBER := 1;\n  VNREGISTRO       NUMBER;\n  VNERRO           EXCEPTION;\n  VNFENCONTRADA    BOOLEAN;\nBEGIN\n  -- 1º Bloco: Cancelamento e ajuste de movimento de estoque\n  UPDATE PCMOV\n    SET DTCANCEL = TRUNC(SYSDATE)\n    WHERE NUMTRANSENT = V_NUMTRANSENT;\n\n  FOR REG IN (SELECT *\n      FROM PCMOV\n      WHERE NUMTRANSENT = V_NUMTRANSENT)\n  LOOP\n    VNUMTRANSITEMOLD := REG.NUMTRANSITEM;\n    SELECT DFSEQ_PCMOVCOMPLE.nextval\n      INTO VNUMTRANSITEMNEW\n      FROM DUAL;\n    REG.NUMTRANSITEM := VNUMTRANSITEMNEW;\n    REG.DTMOV := TRUNC(SYSDATE);\n    REG.QT := (REG.QT * (-1));\n    REG.QTCONT := (REG.QTCONT * (-1));\n    REG.QTBLOQUEADA := (REG.QTBLOQUEADA * (-1));\n    REG.QTAVARIA := (REG.QTAVARIA * (-1));\n    REG.QTULTENTANT := (REG.QTULTENTANT * (-1));\n    REG.QTDEVOL := (REG.QTDEVOL * (-1));\n    REG.NUMTRANSDEV := 0;\n    REG.CODFUNCLANC := 1;\n    INSERT INTO PCMOV\n    VALUES REG;\n\n    FOR REGC IN (SELECT *\n        FROM PCMOVCOMPLE C\n        WHERE C.NUMTRANSITEM = VNUMTRANSITEMOLD)\n    LOOP\n      REGC.NUMTRANSITEM := VNUMTRANSITEMNEW;\n      REGC.DTREGISTRO := SYSDATE;\n      INSERT INTO PCMOVCOMPLE\n      VALUES REGC;\n    END LOOP;\n  END LOOP;\n\n  -- 2º Bloco: Ajuste de saldo de estoque de rolos\n  FOR REGISTROS IN (SELECT PCMOVROLO.CODFILIAL,\n                           PCMOVROLO.CODPROD,\n                           PCMOVROLO.NUMROLO,\n                           NVL(PCMOVROLO.QT, 0) AS QT\n      FROM PCMOVROLO\n      WHERE PCMOVROLO.NUMTRANSENT = V_NUMTRANSENT)\n  LOOP\n    -- Atualizando o saldo\n    UPDATE PCESTROLO\n      SET QT = NVL(QT, 0) - REGISTROS.QT\n      WHERE PCESTROLO.CODFILIAL = REGISTROS.CODFILIAL\n      AND PCESTROLO.NUMROLO = REGISTROS.NUMROLO\n      AND PCESTROLO.CODPROD = REGISTROS.CODPROD;\n\n    -- Excluindo registros zerados\n    DELETE FROM PCESTROLO\n      WHERE ((PCESTROLO.QT = 0)\n        OR (PCESTROLO.QT IS NULL))\n        AND PCESTROLO.CODFILIAL = REGISTROS.CODFILIAL\n        AND PCESTROLO.NUMROLO = REGISTROS.NUMROLO\n        AND PCESTROLO.CODPROD = REGISTROS.CODPROD;\n  END LOOP;\n\n  -- Lançando a contra-partida negativa\n  INSERT INTO PCMOVROLO (\n    CODFILIAL, NUMTRANSENT, NUMTRANSVENDA, NUMROLO, QT, CODOPER, DTMOVROLO, CODFUNC, CODPROD\n  ) (SELECT CODFILIAL,\n            NUMTRANSENT,\n            NUMTRANSVENDA,\n            NUMROLO,\n            (QT * (-1)),\n            CODOPER,\n            TRUNC(SYSDATE),\n            CODFUNC,\n            CODPROD\n      FROM PCMOVROLO\n      WHERE PCMOVROLO.NUMTRANSENT = V_NUMTRANSENT);\n\n  -- 3º Bloco: Ajustes nas tabelas PCNFBASE e PCNFENTPISCOFINS\n  /* APAGANDO A TABELA PCNFBASE DO FRETE */\n  DELETE FROM PCNFBASE\n    WHERE PCNFBASE.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n          FROM PCNFENTFRETE\n          WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT\n            AND (NVL(PCNFENTFRETE.CALCICMSFRETEFOBCSTPROD, NVL(PARAMFILIAL.OBTERCOMOVARCHAR2('CALCICMSFRETEFOBCSTPROD', NULL), 'N')) = 'S'))\n      AND PCNFBASE.CODCONT = PARAMFILIAL.OBTERCOMONUMBER('CON_CODCONTFRE');\n\n  /* RECRIANDO A TABELA PCNFBASE DO FRETE */\n  FOR REGISTROS IN (WITH PESOFRETE AS (SELECT PCNFENTFRETE.NUMTRANSENT\n            FROM PCNFENTFRETE\n            WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT\n              AND (NVL(PCNFENTFRETE.CALCICMSFRETEFOBCSTPROD, NVL(PARAMFILIAL.OBTERCOMOVARCHAR2('CALCICMSFRETEFOBCSTPROD', NULL), 'N')) = 'S')\n            GROUP BY PCNFENTFRETE.NUMTRANSENT) SELECT PESOFRETE.NUMTRANSENT,\n                                                      PCMOVCOMPLE.CODSITTRIBICMSFRETEFOB,\n                                                      PCMOVCOMPLE.PERCICMSFRETEFOB,\n                                                      MAX(PCMOVCOMPLE.PERCICMSCUSTOFRETEFOB) PERCICMSCUSTOFRETEFOB,\n                                                      ROUND(SUM(PCMOVCOMPLE.VLBASEICMSFRETEFOB * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLBASEICMSFRETEFOB,\n                                                      ROUND(SUM(PCMOVCOMPLE.VLICMSFRETEFOB * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLICMSFRETEFOB,\n                                                      ROUND(SUM(PCMOV.VLFRETECONHEC * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLCONTABILFRETEFOB,\n                                                      (SELECT PCNFENT.CODFISCAL\n                                                          FROM PCNFENT\n                                                          WHERE PCNFENT.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n                                                                FROM PCNFENTFRETE\n                                                                WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT)\n                                                            AND PCNFENT.CODCONT = PCNFENT.CODCONTFRE\n                                                            AND ROWNUM = 1) CODFISCAL\n      FROM PCMOV,\n           PCMOVCOMPLE,\n           PESOFRETE\n      WHERE PCMOV.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENTNF\n            FROM PCNFENTFRETE\n            WHERE NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n                  FROM PCNFENTFRETE\n                  WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT)\n              AND (NVL(PCNFENTFRETE.CALCICMSFRETEFOBCSTPROD, NVL(PARAMFILIAL.OBTERCOMOVARCHAR2('CALCICMSFRETEFOBCSTPROD', NULL), 'N')) = 'S'))\n        AND PCMOV.NUMTRANSITEM = PCMOVCOMPLE.NUMTRANSITEM\n        AND PCMOV.DTCANCEL IS NULL\n      GROUP BY PESOFRETE.NUMTRANSENT,\n               PCMOVCOMPLE.CODSITTRIBICMSFRETEFOB,\n               PCMOVCOMPLE.PERCICMSFRETEFOB)\n  LOOP\n    IF NVL(REGISTROS.NUMTRANSENT, 0) > 0\n    THEN\n      INSERT INTO PCNFBASE (\n        PCNFBASE.ALIQUOTA, PCNFBASE.VLBASE, PCNFBASE.VLICMS, PCNFBASE.NUMTRANSENT, PCNFBASE.CODCONT, PCNFBASE.CODFISCAL, PCNFBASE.VLCONTABIL, PCNFBASE.PERCREDICMSCUSTO, PCNFBASE.SITTRIBUT, PCNFBASE.NUMTRANSPISCOFINS\n      )\n      VALUES (REGISTROS.PERCICMSFRETEFOB, REGISTROS.VLBASEICMSFRETEFOB, REGISTROS.VLICMSFRETEFOB, REGISTROS.NUMTRANSENT, PARAMFILIAL.OBTERCOMONUMBER('CON_CODCONTFRE'), REGISTROS.CODFISCAL, REGISTROS.VLCONTABILFRETEFOB, REGISTROS.PERCICMSCUSTOFRETEFOB, REGISTROS.CODSITTRIBICMSFRETEFOB, DFSEQ_PCNFENTPISCOFINS.nextval);\n    END IF;\n  END LOOP;\n\n  /* APAGANDO A TABELA PCNFENTPISCOFINS DO FRETE */\n  DELETE FROM PCNFENTPISCOFINS\n    WHERE PCNFENTPISCOFINS.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n          FROM PCNFENTFRETE\n          WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT);\n\n  /* RECRIANDO A TABELA PCNFENTPISCOFINS DO FRETE*/\n  FOR REGISTROS IN (WITH PESOFRETE AS (SELECT PCNFENTFRETE.NUMTRANSENT\n            FROM PCNFENTFRETE\n            WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT\n              AND (NVL(PCNFENTFRETE.CALCICMSFRETEFOBCSTPROD, NVL(PARAMFILIAL.OBTERCOMOVARCHAR2('CALCICMSFRETEFOBCSTPROD', NULL), 'N')) = 'S')\n            GROUP BY PCNFENTFRETE.NUMTRANSENT) SELECT PESOFRETE.NUMTRANSENT,\n                                                      PCMOVCOMPLE.CODSITTRIBPISCOFINSFRETEFOB,\n                                                      ROUND(PCMOVCOMPLE.PERPISFRETEFOB, 2) PERPISFRETEFOB,\n                                                      ROUND(PCMOVCOMPLE.PERCOFINSFRETEFOB, 2) PERCOFINSFRETEFOB,\n                                                      ROUND(SUM(PCMOVCOMPLE.VLBASEPISCOFINSFRETEFOB * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLBASEPISCOFINSFRETEFOB,\n                                                      ROUND(SUM(PCMOVCOMPLE.VLCREDPISFRETEFOB * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLCREDPISFRETEFOB,\n                                                      ROUND(SUM(PCMOVCOMPLE.VLCREDCOFINSFRETEFOB * DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)), 2) VLCREDCOFINSFRETEFOB,\n                                                      (SELECT PCNFENT.CODFISCAL\n                                                          FROM PCNFENT\n                                                          WHERE PCNFENT.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n                                                                FROM PCNFENTFRETE\n                                                                WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT)\n                                                            AND PCNFENT.CODCONT = PCNFENT.CODCONTFRE\n                                                            AND ROWNUM = 1) CODFISCAL,\n                                                      PCNFBASE.NUMTRANSPISCOFINS\n      FROM PCMOV,\n           PCMOVCOMPLE,\n           PESOFRETE,\n           PCNFBASE\n      WHERE PCMOV.NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENTNF\n            FROM PCNFENTFRETE\n            WHERE NUMTRANSENT IN (SELECT PCNFENTFRETE.NUMTRANSENT\n                  FROM PCNFENTFRETE\n                  WHERE PCNFENTFRETE.NUMTRANSENTNF = V_NUMTRANSENT))\n        AND PESOFRETE.NUMTRANSENT = PCNFBASE.NUMTRANSENT\n        AND PCMOV.NUMTRANSITEM = PCMOVCOMPLE.NUMTRANSITEM\n        AND PCNFBASE.SITTRIBUT = PCMOVCOMPLE.CODSITTRIBICMSFRETEFOB\n        AND PCMOV.DTCANCEL IS NULL\n      GROUP BY PESOFRETE.NUMTRANSENT,\n               PCMOVCOMPLE.CODSITTRIBPISCOFINSFRETEFOB,\n               ROUND(PCMOVCOMPLE.PERPISFRETEFOB, 2),\n               ROUND(PCMOVCOMPLE.PERCOFINSFRETEFOB, 2),\n               PCNFBASE.NUMTRANSPISCOFINS)\n  LOOP\n    IF NVL(REGISTROS.NUMTRANSENT, 0) > 0\n    THEN\n      INSERT INTO PCNFENTPISCOFINS (\n        PCNFENTPISCOFINS.CODTRIBPISCOFINS, PCNFENTPISCOFINS.VLBASEPIS, PCNFENTPISCOFINS.VLBASECOFINS, PCNFENTPISCOFINS.PERPIS, PCNFENTPISCOFINS.PERCOFINS, PCNFENTPISCOFINS.VLPIS, PCNFENTPISCOFINS.VLCOFINS, PCNFENTPISCOFINS.NUMTRANSENT, PCNFENTPISCOFINS.NUMTRANSPISCOFINS\n      )\n      VALUES (REGISTROS.CODSITTRIBPISCOFINSFRETEFOB, REGISTROS.VLBASEPISCOFINSFRETEFOB, REGISTROS.VLBASEPISCOFINSFRETEFOB, REGISTROS.PERPISFRETEFOB, REGISTROS.PERCOFINSFRETEFOB, REGISTROS.VLCREDPISFRETEFOB, REGISTROS.VLCREDCOFINSFRETEFOB, REGISTROS.NUMTRANSENT, REGISTROS.NUMTRANSPISCOFINS);\n    END IF;\n  END LOOP;\n\n  UPDATE PCNFENT\n    SET ESPECIE = 'NF', SITUACAONFE = 101\n    WHERE NUMTRANSENT = V_NUMTRANSENT\n    AND CODCONT = 100001.000000;\n\n  UPDATE PCNFBASE\n    SET VLBASE = 0, VLICMS = 0, ALIQUOTA = 0, PERCREDICMSCUSTO = 0\n    WHERE NUMTRANSENT = V_NUMTRANSENT;\n\n  UPDATE PCNFENTXML\n    SET PCNFENTXML.NUMTRANSENT = NULL, PCNFENTXML.SITUACAO = NULL\n    WHERE PCNFENTXML.NUMTRANSENT = V_NUMTRANSENT;\n\n  UPDATE PCCOMPVENDAPEND\n    SET PCCOMPVENDAPEND.DTCANCEL = TRUNC(SYSDATE)\n    WHERE PCCOMPVENDAPEND.NUMTRANSENT = V_NUMTRANSENT\n    AND (NVL(PCCOMPVENDAPEND.LIBERADOVENDA, 'N') = 'S');\n  IF SQL % ROWCOUNT = 0\n  THEN\n    DELETE FROM PCCOMPVENDAPEND\n      WHERE NUMTRANSENT = V_NUMTRANSENT;\n  END IF;\n\n  FOR NOTA IN (SELECT DISTINCT PCMOV.NUMPED\n      FROM PCMOV\n      WHERE PCMOV.NUMTRANSENT = V_NUMTRANSENT\n        AND NVL(PCMOV.NUMPED, 0) > 0)\n  LOOP\n    UPDATE PCITEM\n      SET PCITEM.QTENTREGUE = 0, PCITEM.CODFUNCALTER = V_CODFUNC\n      WHERE PCITEM.NUMPED = NOTA.NUMPED\n      AND NVL(PCITEM.QTENTREGUE, 0) > 0;\n    FOR REG IN (SELECT PCITEM.NUMPED,\n                       PCITEM.CODPROD,\n                       SUM(NVL(PCITEM.QTPEDIDA, 0)) QTPEDIDA,\n                       NVL(PCITEM.PRODBONIFICADO, 'N') PRODBONIFICADO,\n                       NUMSEQ\n        FROM PCITEM\n        WHERE PCITEM.NUMPED = NOTA.NUMPED\n        GROUP BY PCITEM.NUMPED,\n                 PCITEM.CODPROD,\n                 NVL(PCITEM.PRODBONIFICADO, 'N'),\n                 NUMSEQ)\n    LOOP\n      FOR MOV IN (SELECT SUM(DECODE(NVL(PCMOV.QT, 0), 0, PCMOV.QTCONT, PCMOV.QT)) QTENTREGUE\n          FROM PCMOV,\n               PCNFENT\n          WHERE PCMOV.DTCANCEL IS NULL\n            AND PCMOV.CODOPER LIKE 'E%'\n            AND PCMOV.NUMTRANSENT = PCNFENT.NUMTRANSENT\n            AND ((PCNFENT.CODCONT = PCNFENT.CODCONTFOR)\n            OR (PCNFENT.TIPODESCARGA = 'S'))\n            AND PCMOV.NUMPED = REG.NUMPED\n            AND PCMOV.CODPROD = REG.CODPROD\n            AND PCMOV.PRODBONIFICADO = CASE WHEN PCNFENT.TIPODESCARGA = '5' THEN 'S' ELSE REG.PRODBONIFICADO END\n            AND PCMOV.NUMSEQPED = REG.NUMSEQ)\n      LOOP\n        IF (MOV.QTENTREGUE > 0)\n        THEN\n          FOR ITEM IN (SELECT PCITEM.ROWID LINHA,\n                              PCITEM.QTPEDIDA\n              FROM PCITEM\n              WHERE PCITEM.NUMPED = REG.NUMPED\n                AND PCITEM.CODPROD = REG.CODPROD\n                AND NVL(PCITEM.PRODBONIFICADO, 'N') = REG.PRODBONIFICADO\n                AND NUMSEQ = REG.NUMSEQ)\n          LOOP\n            IF (MOV.QTENTREGUE > 0)\n            THEN\n              IF (ITEM.QTPEDIDA > MOV.QTENTREGUE)\n              THEN\n                VQTENTREGUE := MOV.QTENTREGUE;\n                MOV.QTENTREGUE := 0;\n              ELSE\n                VQTENTREGUE := ITEM.QTPEDIDA;\n                MOV.QTENTREGUE := MOV.QTENTREGUE - ITEM.QTPEDIDA;\n              END IF;\n              UPDATE PCITEM\n                SET PCITEM.QTENTREGUE = VQTENTREGUE, PCITEM.CODFUNCALTER = V_CODFUNC\n                WHERE PCITEM.ROWID = ITEM.LINHA;\n            END IF;\n          END LOOP;\n        END IF;\n      END LOOP;\n    END LOOP;\n    FOR REGISTROS IN (SELECT PCPEDIDO.NUMPED,\n                             SUM((NVL(PCITEM.PLIQUIDO, 0)\n                             + NVL(PCITEM.VLST, 0)\n                             + NVL(PCITEM.VLFECP, 0)\n                             + NVL(PCITEM.VLIPI, 0)\n                             + NVL(PCITEM.VLSEGURO, 0)\n                             + NVL(PCITEM.VLDESPDENTRONF, 0)\n                             + NVL(PCITEM.VLFRETE, 0))\n                             * NVL(PCITEM.QTENTREGUE, 0)) VLENTREGUE\n        FROM PCPEDIDO,\n             PCITEM\n        WHERE PCPEDIDO.NUMPED = PCITEM.NUMPED\n          AND PCPEDIDO.NUMPED = NOTA.NUMPED\n        GROUP BY PCPEDIDO.NUMPED)\n    LOOP\n      UPDATE PCPEDIDO\n        SET VLENTREGUE = REGISTROS.VLENTREGUE\n        WHERE PCPEDIDO.NUMPED = REGISTROS.NUMPED;\n    END LOOP;\n  END LOOP;\n\n  BEGIN\n    VNFENCONTRADA := FALSE;\n    FOR REGISTROS IN (SELECT PCNFENT.ROWID RID,\n                             PCNFENT.NUMTRANSENT,\n                             PCNFENT.GERANFVENDA,\n                             PCNFENT.CODCONT,\n                             NVL(PCNFENT.TIPODESCARGA, '1') TIPODESCARGA\n        FROM PCNFENT,\n             PCCONSUM\n        WHERE PCNFENT.NUMTRANSENT = V_NUMTRANSENT\n          AND (((PCNFENT.CODCONT = NVL(PCNFENT.CODCONTFOR, PCCONSUM.CODCONTFOR))\n          OR ((PCNFENT.CODCONT = PCCONSUM.CODCONTOUT)\n          AND PCNFENT.CODFISCAL = 599)\n          OR ((PCNFENT.CODCONT = NVL(PARAMFILIAL.OBTERCOMONUMBER('CODCONTATFCPGUIAENT', 99), 0))\n          AND PCNFENT.CODFISCAL = 599)\n          OR ((PCNFENT.CODCONT = NVL(PARAMFILIAL.OBTERCOMONUMBER('CODCONTASTGUIAENT', 99), 0))\n          AND PCNFENT.CODFISCAL = 599))\n          OR ((PCNFENT.CODCONT = PCCONSUM.CODCONTAJUSTEEST)\n          AND (PCNFENT.TIPODESCARGA = '4'))\n          OR (PCNFENT.TIPODESCARGA = 'S')))\n    LOOP\n      VNFENCONTRADA := TRUE;\n      SELECT SUM(NVL(PCLANC.VALOR, 0))\n        INTO VNREGISTRO\n        FROM PCLANC,\n             PCCONSUM\n        WHERE (((PCLANC.NUMTRANSENTNF IS NOT NULL)\n          AND (REGISTROS.CODCONT = PCCONSUM.CODCONTFOR))\n          AND ((((PCLANC.CODCONTA = PCCONSUM.CODCONTFRE)\n          OR (PCLANC.CODCONTA = NVL(PARAMFILIAL.OBTERCOMONUMBER('CODCONTSERV'), PCCONSUM.CODCONTFRE)))\n          AND (PCLANC.NUMTRANSENTNF = V_NUMTRANSENT))\n          OR ((PCLANC.CODCONTA = PCCONSUM.CODCONTFOR)\n          AND (PCLANC.NUMTRANSENT = V_NUMTRANSENT))))\n          OR ((PCLANC.NUMTRANSENTNF IS NULL)\n          AND (PCLANC.NUMTRANSENT = REGISTROS.NUMTRANSENT)\n          AND (PCLANC.CODCONTA = REGISTROS.CODCONT)\n          AND (((PCLANC.CODCONTA <> PCCONSUM.CODCONTFRE)\n          AND (PCLANC.CODCONTA <> NVL(PARAMFILIAL.OBTERCOMONUMBER('CODCONTSERV'), PCCONSUM.CODCONTFRE)))\n          OR (((PCLANC.CODCONTA = PCCONSUM.CODCONTFRE)\n          OR (PCLANC.CODCONTA = NVL(PARAMFILIAL.OBTERCOMONUMBER('CODCONTSERV'), PCCONSUM.CODCONTFRE)))\n          AND (PCLANC.LOCALIZACAO LIKE '%' || V_NUMNOTA || '%'))));\n      IF ((NVL(VNREGISTRO, 0) = 0)\n        OR (REGISTROS.TIPODESCARGA IN ('2', '4', 'S', 'A', '5', 'R', 'M', 'G', 'H', 'J')))\n      THEN\n        UPDATE PCNFENT\n          SET PCNFENT.VLTOTAL = 0, PCNFENT.VLTOTGER = 0, PCNFENT.VLST = 0, PCNFENT.VLIPI = 0, PCNFENT.VLFRETE = 0, PCNFENT.VLOUTRAS = 0, PCNFENT.VLBASEIPI = 0, PCNFENT.NUMBONUS = NULL, PCNFENT.ESPECIE = DECODE(REGISTROS.GERANFVENDA, 'S', 'NF', 'OE'), PCNFENT.OBS = 'NF CANCELADA', PCNFENT.DTCANCEL = TRUNC(SYSDATE)\n          WHERE PCNFENT.ROWID = REGISTROS.RID;\n      ELSE\n        RAISE VNERRO;\n      END IF;\n    END LOOP;\n    /*caso não encontre nota fiscal deve reproduzir erro */\n    IF NOT VNFENCONTRADA\n    THEN\n      RAISE VNERRO;\n    END IF;\n\n  EXCEPTION\n    WHEN VNERRO THEN RAISE_APPLICATION_ERROR(-20051, 'Erro cancelamento do cabeçalho(PCNFENT).');\n  END;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2420, 160], "id": "afd42296-b3f5-4827-9e1b-b00c92a57dee", "name": "CANCELAR NF", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMENS", "mode": "list", "cachedResultName": "PCMENS"}, "valuesToSend": {"values": [{"name": "CODUSUR", "value": "1"}, {"name": "DATA", "value": "={{ $('Loop').item.json.DATA }}"}, {"name": "MENS1", "value": "=NF.: {{ $('Loop').item.json.NUMNOTA }} ** CANCELADA **"}, {"name": "MENS2", "value": "=Valor: {{ $('Loop').item.json.VLTOTAL }}"}, {"name": "MENS3", "value": "=Cliente: {{ $('Loop').item.json.PARCEIRO }}"}, {"name": "MENS4", "value": "Motivo: NOTA CANCELADA NO MERCADO LIVRE"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2600, -60], "id": "9f83a2e3-208a-46fc-b104-35c2f34e3045", "name": "PCMENS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCNFSAID", "mode": "list", "cachedResultName": "PCNFSAID"}, "where": {"values": [{"column": "CHAVENFE", "value": "={{ $('Loop').item.json.CHAVENFE }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2780, -60], "id": "65de877f-c858-46a4-b7ff-8b15ccdb914a", "name": "SITUACAONFE", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n  RESULTADO        VARCHAR2(4000);\n  SAIR             BOOLEAN;\n  MEDIDOR          NUMBER;\n  VDATAINICIAL     DATE;\n  VDATAFINAL       DATE;\n  VDATAINICIALTEMP DATE;\n  VDATAFINALTEMP   DATE;\n  VDATATEMP        DATE;\n  VCODFILIAL       VARCHAR2(2);\nBEGIN\n  DELETE FROM PCGERALIVROSFISCAIS_TEMP;\n  COMMIT;\n\n  SAIR := FALSE;\n  MEDIDOR := 0;\n  VDATAINICIAL := TO_DATE('{{ $('Loop').item.json.DATA }}', 'DD/MM/YYYY');\n  VDATAFINAL := TO_DATE('{{ $('Loop').item.json.DATA }}', 'DD/MM/YYYY');\n  VDATAINICIALTEMP := VDATAINICIAL;\n  VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n  VCODFILIAL := {{ $('Loop').item.json.CODFILIAL }};\n\n  WHILE (NOT SAIR)\n    LOOP\n      IF VDATAFINALTEMP > VDATAFINAL\n      THEN\n        VDATAFINALTEMP := VDATAFINAL;\n      END IF;\n\n      GERALIVRO_SAIDA(VDATAINICIALTEMP, VDATAFINALTEMP, VCODFILIAL, 0, 9999999999, RESULTADO);\n      INSERT INTO PCGERALIVROSFISCAIS_TEMP (\n        CODFILIAL, DTMOV, TIPO_ES, RESULTADO\n      )\n      VALUES (VCODFILIAL, VDATAFINALTEMP, 'S', SUBSTR(RESULTADO, 1, 4000));\n\n\n      IF (VDATAFINALTEMP >= VDATAFINAL)\n      THEN\n        SAIR := TRUE;\n      ELSE\n        VDATAINICIALTEMP := VDATAFINALTEMP + 1;\n        VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n      END IF;\n      COMMIT;\n    END LOOP;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [2960, -60], "id": "160596f5-4e2f-459c-b68c-ef8e27a071a3", "name": "GERALIVRO_SAIDA", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"options": {}}, "id": "76bc553a-d0bd-47af-ba58-f9412c169e2b", "name": "Loop", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1860, -80]}, {"parameters": {"options": {}}, "id": "425045a4-aaca-4277-9380-9c0c9a2f3d97", "name": "Loop1", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1860, 140]}], "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Validar NF", "type": "main", "index": 0}]]}, "Validar NF": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[], [{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "Cancelar NF Saída": {"main": [[{"node": "PROXCODLOG", "type": "main", "index": 0}]]}, "PROXCODLOG": {"main": [[{"node": "PCLOGFATURAMENTO", "type": "main", "index": 0}]]}, "PCLOGFATURAMENTO": {"main": [[{"node": "PCMENS", "type": "main", "index": 0}]]}, "Cancelar NF Entrada": {"main": [[{"node": "PCNFCAN", "type": "main", "index": 0}]]}, "PCNFCAN": {"main": [[{"node": "CANCELAR NF", "type": "main", "index": 0}]]}, "CANCELAR NF": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "PCMENS": {"main": [[{"node": "SITUACAONFE", "type": "main", "index": 0}]]}, "SITUACAONFE": {"main": [[{"node": "GERALIVRO_SAIDA", "type": "main", "index": 0}]]}, "GERALIVRO_SAIDA": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "Cancelar NF Saída", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Cancelar NF Entrada", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}