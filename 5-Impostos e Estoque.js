// 5-Impostos e Estoque
let results = []

for (let itemIndex = 0; itemIndex < items.length; itemIndex++) {
  const item = items[itemIndex]
  const xmlData = item.json // Usar a estrutura de dados do item atual

  if (
    xmlData &&
    xmlData['nfeProc'] &&
    xmlData['nfeProc']['NFe'] &&
    xmlData['nfeProc']['NFe']['infNFe'] &&
    xmlData['nfeProc']['NFe']['infNFe']['det'] &&
    xmlData['nfeProc']['NFe']['infNFe']['total'] &&
    xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']
  ) {
    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']

    // Certificar-se de que detList seja sempre um array
    if (!Array.isArray(detList)) {
      detList = [detList]
    }

    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']
    const vProdTotal = parseFloat(ICMSTot['vProd'])
    const vFreteTotal = parseFloat(ICMSTot['vFrete'] || 0)
    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)
    const vOutro = parseFloat(ICMSTot['vOutro'] || 0)
    // Obter a natureza da operação já utilizada para resultItem
    const natOp = xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp']
    
    // Verificar se é uma nota de devolução
    const isNotaDevolucao = xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'] === '4'
    
    // Lista de CFOPs de devolução
    const devolucaoCFOPs = [
      '1201',
      '1202',
      '1203',
      '1204',
      '1208',
      '1209',
      '1212',
      '1213',
      '1214',
      '1215',
      '1216',
      '1410',
      '1411',
      '1503',
      '1504',
      '1505',
      '1506',
      '1553',
      '1660',
      '1661',
      '1662',
      '1918',
      '1919',
      '2201',
      '2202',
      '2203',
      '2204',
      '2208',
      '2209',
      '2212',
      '2213',
      '2214',
      '2215',
      '2216',
      '2410',
      '2411',
      '2503',
      '2504',
      '2505',
      '2506',
      '2553',
      '2660',
      '2661',
      '2662',
      '2918',
      '2919',
      '3201',
      '3202',
      '3211',
      '3212',
      '3503',
      '3553',
      '5201',
      '5202',
      '5208',
      '5209',
      '5210',
      '5213',
      '5214',
      '5215',
      '5216',
      '5410',
      '5411',
      '5412',
      '5413',
      '5503',
      '5553',
      '5555',
      '5556',
      '5660',
      '5661',
      '5662',
      '5918',
      '5919',
      '5921',
      '6201',
      '6202',
      '6208',
      '6209',
      '6210',
      '6213',
      '6214',
      '6215',
      '6216',
      '6410',
      '6411',
      '6412',
      '6413',
      '6503',
      '6553',
      '6555',
      '6556',
      '6660',
      '6661',
      '6662',
      '6918',
      '6919',
      '6921',
      '7201',
      '7202',
      '7210',
      '7211',
      '7212',
      '7553',
      '7556',
      '7930'
    ]

    for (const det of detList) {
      if (det['prod']) {
        const nItem = det['nItem']
        const produto = det['prod']
        const imposto = det['imposto']

        // Ajuste vIPIDevol para itens: para devolução, tenta ambas as tags
        let vIPIDevol = 0
        if (isNotaDevolucao) {
          // Para devolução: tenta vIPIDevol primeiro, senão vIPI do imposto normal
          if (det['impostoDevol'] &&
              det['impostoDevol']['IPI'] &&
              det['impostoDevol']['IPI']['vIPIDevol'] &&
              parseFloat(det['impostoDevol']['IPI']['vIPIDevol']) > 0) {
            vIPIDevol = parseFloat(det['impostoDevol']['IPI']['vIPIDevol']) / parseFloat(produto['qCom'])
          } else if (imposto && 
                     imposto['IPI'] && 
                     imposto['IPI']['IPITrib'] && 
                     imposto['IPI']['IPITrib']['vIPI'] &&
                     parseFloat(imposto['IPI']['IPITrib']['vIPI']) > 0) {
            vIPIDevol = parseFloat(imposto['IPI']['IPITrib']['vIPI']) / parseFloat(produto['qCom'])
          }
        } else {
          // Para outras notas: comportamento normal
          vIPIDevol = det['impostoDevol'] &&
                      det['impostoDevol']['IPI'] &&
                      det['impostoDevol']['IPI']['vIPIDevol']
                        ? parseFloat(det['impostoDevol']['IPI']['vIPIDevol']) / parseFloat(produto['qCom'])
                        : 0
        }

        // Correção no acesso às variáveis PIS e COFINS
        const pis =
          imposto &&
          imposto['PIS'] &&
          (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr'])
        const cofins =
          imposto &&
          imposto['COFINS'] &&
          (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr'])
        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']
        const icms = imposto && imposto['ICMS']
        const vBCUFDest =
          (imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['vBCUFDest']) ||
          0
        const pICMSUFDest =
          (imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['pICMSUFDest']) ||
          0
        const pICMSInter =
          (imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['pICMSInter']) ||
          0
        const pICMSInterPart =
          (imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['pICMSInterPart']) ||
          0
        const pFCPUFDest =
          (imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['pFCPUFDest']) ||
          0
        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']

        const vIPIItem = ipi
          ? parseFloat(ipi['vIPI'] / parseFloat(produto['qCom']) || 0)
          : 0
        const vDescItem =
          parseFloat(produto['vDesc'] / parseFloat(produto['qCom'])) || 0
        const vFreteItem = produto['vFrete']
          ? parseFloat(
              (produto['vFrete'] / parseFloat(produto['qCom'])).toFixed(6)
            )
          : 0
        // Verificar se é devolução
        const isDevolucao = devolucaoCFOPs.includes(produto['CFOP'])
        const PUNITCONT = parseFloat(
          (
            parseFloat(produto['vUnCom']) +
            (isDevolucao ? vIPIDevol : vIPIItem) -
            vDescItem
          ).toFixed(3)
        )

        // Encontrar o primeiro objeto ICMS que existe no XML
        const icmsObj = [
          'ICMS00',
          'ICMS10',
          'ICMS20',
          'ICMS30',
          'ICMS40',
          'ICMS50',
          'ICMS60',
          'ICMS70',
          'ICMS80',
          'ICMSSN101',
          'ICMSSN102',
          'ICMSSN103',
          'ICMSSN201',
          'ICMSSN202',
          'ICMSSN203',
          'ICMSSN300',
          'ICMSSN400',
          'ICMSSN500',
          'ICMSSN900'
        ].find(obj => icms && icms[obj])

        // Calcular os valores conforme as fórmulas fornecidas
        const vICMSSubstituto = parseFloat(
          ((icms && icms[icmsObj]?.['vICMSSubstituto']) || 0) / produto['qCom']
        )

        const vBCST = parseFloat(
          ((icms && icms[icmsObj]?.['vBCST']) || 0) / produto['qCom']
        )

        const vICMSST = parseFloat(
          ((icms && icms[icmsObj]?.['vICMSST']) || 0) / produto['qCom']
        )

        const pICMSST = parseFloat((icms && icms[icmsObj]?.['pICMSST']) || 0)

        const pMVAST = parseFloat((icms && icms[icmsObj]?.['pMVAST']) || 0)

        // BC de IPI  = V. TOTAL PRODUTOS
        const VLBASEIPI = parseFloat(produto['vProd']) / parseFloat(produto['qCom'])

        const VLICMSPARTDEST = parseFloat(
          ((imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['vICMSUFDest']) ||
            0) / produto['qCom']
        )

        const VLICMSPARTREM = parseFloat(
          ((imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['vICMSUFRemet']) ||
            0) / produto['qCom']
        )

        const VLFCPPART = parseFloat(
          ((imposto &&
            imposto['ICMSUFDest'] &&
            imposto['ICMSUFDest']['vFCPUFDest']) ||
            0) / produto['qCom']
        )
        // Obter o valor da tag CST para PIS ou COFINS
        const CST_PIS_COFINS = pis
          ? pis['CST'] ||
            pis['PISAliq']?.['CST'] ||
            pis['PISOutr']?.['CST'] ||
            '00'
          : cofins
          ? cofins['CST'] ||
            cofins['COFINSAliq']?.['CST'] ||
            cofins['COFINSOutr']?.['CST'] ||
            '00'
          : '00'
        const xPed = produto['xPed'] || ''
        // Adicione o resultado ao array 'results'
        const pICMS = icms ? parseFloat(icms[icmsObj]?.['pICMS']) : 0
        const resultItem = {
          nItem: nItem,
          cProd: produto['cProd'],
          xProd: produto['xProd'],
          ncm: produto['NCM'],
          CODCEST: produto['CEST'] || '',
          cEAN: produto['cEAN'],
          uCom: produto['uCom'],
          qCom: produto['qCom'],
          vUnCom: produto['vUnCom'],
          vProd: produto['vProd'],
          vProdTotal: vProdTotal,
          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],
          refNFe: [],
          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],
          natOp,
          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],
          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],
          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],
          vOutro: vOutro,
          vFreteItem: vFreteItem,
          vFreteTotal: vFreteTotal,
          vDesc:
            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vDesc'],
          vDescItem: vDescItem,
          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],
          ufDest:
            xmlData['nfeProc']['NFe']['infNFe']['dest']['enderDest']['UF'],
          ufEmit:
            xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],
          pPIS: pis ? pis['pPIS'] || 0 : 0,
          vPIS: pis ? parseFloat(pis['vPIS'] / produto['qCom']) || 0 : 0,
          pCOFINS: cofins ? cofins['pCOFINS'] || 0 : 0,
          vCOFINS: cofins
            ? parseFloat(cofins['vCOFINS'] / produto['qCom']) || 0
            : 0,
          pIPI: ipi ? ipi['pIPI'] || 0 : 0,
          vIPIItem,
          CODSITTRIBIPI: ipi ? ipi['CST'] || 0 : 0,
          //VLBASEIPI items
          vBC_IPI: imposto?.['IPI']?.['IPITrib']?.['vBC']
            ? parseFloat(imposto['IPI']['IPITrib']['vBC'] / produto['qCom'])
            : 0,
          vBC_PIS: pis ? pis['vBC'] || 0 : 0,
          vBC_COFINS: cofins ? cofins['vBC'] || 0 : 0,
          vICMS: parseFloat(icms && icms[icmsObj]?.['vICMS']) || 0,
          pICMS: pICMS,
          CST: icms ? parseFloat(icms[icmsObj]?.['CST']) || '00' : '00',
          CST_PIS_COFINS, // Variável única para CST
					// remova o vlfreteitem da PCMOVCOMPLETE nas devoluções para não duplicar o valor de frete na BASEICMS do livro fiscal
          BASEICMS: BASEICMS,
          STBCR: parseFloat(
            ((icms && icms[icmsObj]?.['vICMSSTRet']) || 0) / produto['qCom']
          ),
          VLICMSBCR: vICMSSubstituto,
          BASEICST: vBCST,
          pST: icms ? parseFloat(icms[icmsObj]?.['pST']) || 0 : 0,
          ST: vICMSST,
          PERCST: pICMSST,
          IVA: pMVAST,
          VLBASEIPI: VLBASEIPI || 0,
          vBCUFDest: vBCUFDest,
          pICMSUFDest: pICMSUFDest,
          pICMSInter: pICMSInter,
          pICMSInterPart: pICMSInterPart,
          VLICMSPARTDEST: VLICMSPARTDEST,
          VLICMSPARTREM: VLICMSPARTREM,
          pFCPUFDest: pFCPUFDest,
          VLFCPPART: VLFCPPART,
          CFOP: produto['CFOP'],
          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],
          CGCENT: dest['CNPJ'] || dest['CPF'] || null,
          PUNITCONT: PUNITCONT,
          VLBASEPISCOFINS: isDevolucao
            ? imposto?.['PIS']?.['PISOutr']?.['vBC']
              ? parseFloat(imposto['PIS']['PISOutr']['vBC'])
              : imposto?.['COFINS']?.['COFINSOutr']?.['vBC']
              ? parseFloat(imposto['COFINS']['COFINSOutr']['vBC'])
              : 0
            : imposto?.['PIS']?.['PISAliq']?.['vBC']
            ? parseFloat(imposto['PIS']['PISAliq']['vBC'])
            : imposto?.['COFINS']?.['COFINSAliq']?.['vBC']
            ? parseFloat(imposto['COFINS']['COFINSAliq']['vBC'])
            : 0,
          xPed,
          vIPIDevol
        }

        // Adicione refNFe se existir
        if (
          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&
          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
        ) {
          const refNFeList = Array.isArray(
            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
          )
            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]

          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')
        }

        // Adicione o resultado ao array 'results' com pairedItem correto
        results.push({
          json: resultItem,
          pairedItem: {
            item: itemIndex,
            input: 0
          }
        })
      }
    }
  }
}

return results