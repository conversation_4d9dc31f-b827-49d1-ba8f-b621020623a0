{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [140, 40], "id": "c7ec2fba-0155-4523-a534-dd5a279ef303", "name": "When clicking ‘Test workflow’"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [680, 40], "id": "52089a51-1111-4f5e-996e-b0b079b500c5", "name": "XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 40], "id": "5ce5f74a-aaee-4eae-ae32-099e894fea95", "name": "Extract from File"}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [320, 40], "id": "f7a9d233-3d4d-4c1b-a1ab-e478d3955875", "name": "Ler XML"}, {"parameters": {"jsCode": "// Livro Fiscal - Entrada/Saida\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det[\"prod\"];\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = { \n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          CFOP: produto[\"CFOP\"],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n\n}\n\nreturn results\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 40], "id": "87f51d18-8a23-448c-b85a-b19ee1caf132", "name": "Extrair dados"}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "tpNF", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1040, 40], "id": "4edb8c2f-bf5f-4a3c-ac5e-43ad84f07b54", "name": "Remove Duplicates"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [1420, -40], "id": "b75afe36-67af-4385-9e5f-c9b333f79d8c", "name": "Limit"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n  RESULTADO        VARCHAR2(4000);\n  SAIR             BOOLEAN;\n  MEDIDOR          NUMBER;\n  VDATAINICIAL     DATE;\n  VDATAFINAL       DATE;\n  VDATAINICIALTEMP DATE;\n  VDATAFINALTEMP   DATE;\n  VDATATEMP        DATE;\n  VCODFILIAL       VARCHAR2(2);\nBEGIN\n  DELETE FROM PCGERALIVROSFISCAIS_TEMP;\n  COMMIT;\n\n  SAIR := FALSE;\n  MEDIDOR := 0;\n  VDATAINICIAL := TO_DATE('{{ $json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY');\n  VDATAFINAL := TO_DATE('{{ $json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY');\n  VDATAINICIALTEMP := VDATAINICIAL;\n  VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n  /* Pegar o código da filial */\n  SELECT CODIGO INTO VCODFILIAL FROM PCFILIAL WHERE APENASNUMEROS(CGC) = APENASNUMEROS('{{ $json.CGCFILIAL }}');\n\n  WHILE (NOT SAIR)\n    LOOP\n      IF VDATAFINALTEMP > VDATAFINAL\n      THEN\n        VDATAFINALTEMP := VDATAFINAL;\n      END IF;\n\n      GERALIVRO_SAIDA(VDATAINICIALTEMP, VDATAFINALTEMP, VCODFILIAL, 0, 9999999999, RESULTADO);\n      INSERT INTO PCGERALIVROSFISCAIS_TEMP (\n        CODFILIAL, DTMOV, TIPO_ES, RESULTADO\n      )\n      VALUES (VCODFILIAL, VDATAFINALTEMP, 'S', SUBSTR(RESULTADO, 1, 4000));\n\n\n      IF (VDATAFINALTEMP >= VDATAFINAL)\n      THEN\n        SAIR := TRUE;\n      ELSE\n        VDATAINICIALTEMP := VDATAFINALTEMP + 1;\n        VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n      END IF;\n      COMMIT;\n    END LOOP;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1600, -40], "id": "b9ffd366-6a2b-412e-be5c-144949414343", "name": "GERALIVRO_SAIDA", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [1420, 120], "id": "40a781a2-6a8a-44c9-b02a-a59d50619602", "name": "Limit1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n  RESULTADO        VARCHAR2(4000);\n  SAIR             BOOLEAN;\n  MEDIDOR          NUMBER;\n  VDATAINICIAL     DATE;\n  VDATAFINAL       DATE;\n  VDATAINICIALTEMP DATE;\n  VDATAFINALTEMP   DATE;\n  VDATATEMP        DATE;\n  VCODFILIAL       VARCHAR2(2);\nBEGIN\n  DELETE FROM PCGERALIVROSFISCAIS_TEMP;\n  COMMIT;\n\n  SAIR := FALSE;\n  MEDIDOR := 0;\n  VDATAINICIAL := TO_DATE('{{ $json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY');\n  VDATAFINAL := TO_DATE('{{ $json.dhEmi.split('T')[0].split('-').reverse().join('/') }}', 'DD/MM/YYYY');\n  VDATAINICIALTEMP := VDATAINICIAL;\n  VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n  /* Pegar o código da filial */\n  SELECT CODIGO INTO VCODFILIAL FROM PCFILIAL WHERE APENASNUMEROS(CGC) = APENASNUMEROS('{{ $json.CGCFILIAL }}');\n\n  WHILE (NOT SAIR)\n    LOOP\n      IF VDATAFINALTEMP > VDATAFINAL\n      THEN\n        VDATAFINALTEMP := VDATAFINAL;\n      END IF;\n\n      GERALIVRO_ENTRADA(VDATAINICIALTEMP, VDATAFINALTEMP, VCODFILIAL, 0, 9999999999, RESULTADO);\n\n      INSERT INTO PCGERALIVROSFISCAIS_TEMP (\n        CODFILIAL, DTMOV, TIPO_ES, RESULTADO\n      )\n      VALUES (VCODFILIAL, VDATAFINALTEMP, 'E', SUBSTR(RESULTADO, 1, 4000));\n\n      IF (VDATAFINALTEMP >= VDATAFINAL)\n      THEN\n        SAIR := TRUE;\n      ELSE\n        VDATAINICIALTEMP := VDATAFINALTEMP + 1;\n        VDATAFINALTEMP := VDATAINICIALTEMP + MEDIDOR;\n      END IF;\n      COMMIT;\n    END LOOP;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1600, 120], "id": "752c3fa3-56c9-4f6c-96fa-39127795f497", "name": "GERALIVRO_ENTRADA", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5172ffb-0cc8-4ab1-8002-02aca1c3c889", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "1decf10e-23f2-4307-b386-97fa96f46cb3", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1220, 40], "id": "668f535a-2147-4206-9858-6ebc1a355ae8", "name": "Entrada/Saida"}], "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Entrada/Saida", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "GERALIVRO_SAIDA", "type": "main", "index": 0}]]}, "Limit1": {"main": [[{"node": "GERALIVRO_ENTRADA", "type": "main", "index": 0}]]}, "Entrada/Saida": {"main": [[{"node": "Limit", "type": "main", "index": 0}], [{"node": "Limit1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}