const items = $items() // Obter todos os itens do nó anterior

const result = []
const statusMap = {}

// Primeiro loop para mapear as chaves e os status
items.forEach(item => {
  const xmlData = item.json

  // Verifica se é uma NF cancelada e mapeia a chave
  if (
    xmlData &&
    xmlData['procEventoNFe'] &&
    xmlData['procEventoNFe']['evento'] &&
    xmlData['procEventoNFe']['evento']['infEvento'] &&
    xmlData['procEventoNFe']['evento']['infEvento']['chNFe']
  ) {
    const chNFeCancelada =
      xmlData['procEventoNFe']['evento']['infEvento']['chNFe']
    if (!statusMap[chNFeCancelada]) {
      statusMap[chNFeCancelada] = { aprovada: null, cancelada: null }
    }
    statusMap[chNFeCancelada].cancelada = {
      chNFe: chNFeCancelada,
      status: 'cancelada',
      vinculo: false
    }
  }

  // Verifica se é uma NF aprovada e mapeia a chave
  if (
    xmlData &&
    xmlData['nfeProc'] &&
    xmlData['nfeProc']['protNFe'] &&
    xmlData['nfeProc']['protNFe']['infProt'] &&
    xmlData['nfeProc']['protNFe']['infProt']['chNFe']
  ) {
    const chNFeAprovada = xmlData['nfeProc']['protNFe']['infProt']['chNFe']
    if (!statusMap[chNFeAprovada]) {
      statusMap[chNFeAprovada] = { aprovada: null, cancelada: null }
    }
    statusMap[chNFeAprovada].aprovada = {
      chNFe: chNFeAprovada,
      status: 'aprovada',
      vinculo: false
    }
  }
})

// Segundo loop para definir a coluna 'vinculo' e preparar o resultado final
Object.keys(statusMap).forEach(chNFe => {
  const entry = statusMap[chNFe]
  if (entry.aprovada && entry.cancelada) {
    entry.aprovada.vinculo = true
    entry.cancelada.vinculo = true
  }
  if (entry.aprovada) {
    result.push({ json: entry.aprovada })
  }
  if (entry.cancelada) {
    result.push({ json: entry.cancelada })
  }
})

return result
