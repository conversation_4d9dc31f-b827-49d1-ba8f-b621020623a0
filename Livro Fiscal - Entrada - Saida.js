// Livro Fiscal - Entrada/Saida
let results = []

for (const item of items) {
  const xmlData = item.json // Usar a estrutura de dados do item atual

  if (
    xmlData &&
    xmlData['nfeProc'] &&
    xmlData['nfeProc']['NFe'] &&
    xmlData['nfeProc']['NFe']['infNFe'] &&
    xmlData['nfeProc']['NFe']['infNFe']['det']
  ) {
    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']

    // Certificar-se de que detList seja sempre um array
    if (!Array.isArray(detList)) {
      detList = [detList]
    }

    for (const det of detList) {
      if (det['prod']) {
        const produto = det['prod']
        // Calcular os valores conforme as fórmulas fornecidas
        const resultItem = {
          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],
          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],
          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],
          CFOP: produto['CFOP'],
          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ']
        }
        // Adicione o resultado ao pairedItem
        if (!item.pairedItem) {
          item.pairedItem = {}
        }
        item.pairedItem = resultItem
        // Adicione o resultado ao array 'results'
        results.push(resultItem)
      }
    }
  }
}

return results
