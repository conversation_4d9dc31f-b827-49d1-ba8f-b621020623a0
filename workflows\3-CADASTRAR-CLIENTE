{"nodes": [{"parameters": {}, "id": "814315a4-f3ae-4266-9b0b-6d3bb0b01f69", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2440, 280]}, {"parameters": {"options": {}}, "id": "a69c783e-c6d6-47ec-8c0e-f3ccc352d18b", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-1960, 280]}, {"parameters": {"jsCode": "let results = {};\nlet uniqueKeys = new Set();\n\nfor (let item of items) {\n    const xmlData = item.json; // Usar a estrutura de dados do item atual\n\n    if (xmlData && xmlData[\"nfeProc\"] && xmlData[\"nfeProc\"][\"NFe\"] && xmlData[\"nfeProc\"][\"NFe\"][\"infNFe\"] && xmlData[\"nfeProc\"][\"NFe\"][\"infNFe\"][\"dest\"]) {\n        const dest = xmlData[\"nfeProc\"][\"NFe\"][\"infNFe\"][\"dest\"];\n        const key = dest[\"CNPJ\"] || dest[\"CPF\"];\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes('mercadolivre')\n            ? 'CML'\n            : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes('Bling')\n            ? 'D2C'\n            : '';\n        // Novas variáveis\n        const tpNF = xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'];\n        const cStat = xmlData['nfeProc']['protNFe']['infProt']['cStat'];\n        const natOp = xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'];\n\n        if (key && CODCOB && tpNF && cStat && natOp) {\n            if (!uniqueKeys.has(key)) {\n                uniqueKeys.add(key);\n                if (!results[key]) {\n                    results[key] = {\n                        CGCENT: null,\n                        CONSUMIDORFINAL: 'N',\n                        CONTRIBUIENTE: 'S',\n                        TIPOFJ: 'J',\n                        xMun: dest[\"enderDest\"] && dest[\"enderDest\"][\"xMun\"] || '',\n                        UF: dest[\"enderDest\"] && dest[\"enderDest\"][\"UF\"] || '',\n                        CODCOB: CODCOB,\n                        tpNF: tpNF, // Adicionando tpNF ao resultado\n                        cStat: cStat, // Adicionando cStat ao resultado\n                        natOp: natOp, // Adicionando natOp ao resultado\n                        xNome: dest[\"xNome\"] || '',\n                        IE: dest[\"IE\"] || 'ISENTO',\n                        xLgr: dest[\"enderDest\"] && dest[\"enderDest\"][\"xLgr\"] || '',\n                        nro: dest[\"enderDest\"] && dest[\"enderDest\"][\"nro\"] || '',\n                        xBairro: dest[\"enderDest\"] && dest[\"enderDest\"][\"xBairro\"] || '',\n                        CEP: dest[\"enderDest\"] && dest[\"enderDest\"][\"CEP\"] || '',\n                        cMun: dest[\"enderDest\"] && dest[\"enderDest\"][\"cMun\"] || '',\n                        cPais: dest[\"enderDest\"] && dest[\"enderDest\"][\"cPais\"] || '',\n                    };\n\n                    if (dest[\"CNPJ\"]) {\n                        results[key].CGCENT = dest[\"CNPJ\"];\n                        results[key].CONSUMIDORFINAL = 'N';\n                        results[key].CONTRIBUIENTE = 'S';\n                        results[key].TIPOFJ = 'J';\n                    } else if (dest[\"CPF\"]) {\n                        results[key].CGCENT = dest[\"CPF\"];\n                        results[key].CONSUMIDORFINAL = 'S';\n                        results[key].CONTRIBUIENTE = 'N';\n                        results[key].TIPOFJ = 'F';\n                    }\n                }\n\n                // Vincule o resultado ao item de entrada\n                item.pairedItem = results[key];\n            }\n        }\n    }\n}\n\nconst uniqueResults = items\n  .filter(item => item.pairedItem && item.pairedItem.CGCENT && item.pairedItem.CODCOB && item.pairedItem.tpNF && item.pairedItem.cStat && item.pairedItem.natOp)\n  .map(item => {\n    return {\n        json: {\n            CGCENT: item.pairedItem.CGCENT,\n            CONSUMIDORFINAL: item.pairedItem.CONSUMIDORFINAL,\n            CONTRIBUIENTE: item.pairedItem.CONTRIBUIENTE,\n            TIPOFJ: item.pairedItem.TIPOFJ,\n            xMun: item.pairedItem.xMun,\n            UF: item.pairedItem.UF,\n            CODCOB: item.pairedItem.CODCOB,\n            tpNF: item.pairedItem.tpNF, // Incluindo tpNF nos resultados finais\n            cStat: item.pairedItem.cStat, // Incluindo cStat nos resultados finais\n            natOp: item.pairedItem.natOp, // Incluindo natOp nos resultados finais\n            xNome: item.pairedItem.xNome, // Incluindo xNome nos resultados finais\n            IE: item.pairedItem.IE, // Incluindo IE nos resultados finais\n            xLgr: item.pairedItem.xLgr, // Incluindo xLgr nos resultados finais\n            nro: item.pairedItem.nro, // Incluindo nro nos resultados finais\n            xBairro: item.pairedItem.xBairro, // Incluindo xBairro nos resultados finais\n            CEP: item.pairedItem.CEP, // Incluindo CEP nos resultados finais\n            cMun: item.pairedItem.cMun, // Incluindo cMun nos resultados finais\n            cPais: item.pairedItem.cPais, // Incluindo cPais nos resultados finais\n        }\n    };\n});\n\nreturn uniqueResults;\n"}, "id": "b095a8eb-b4fd-46b8-96fd-cfcc5fbee4ff", "name": "Validar Consumidor Final", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1800, 280], "alwaysOutputData": false, "notesInFlow": false}, {"parameters": {"fieldToSplitOut": "CGCENT", "include": "allOtherFields", "options": {}}, "id": "eb1868d9-b9d8-46ba-9305-6467e8e7cf10", "name": "Remove Underfined", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1640, 280]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "6bab1d94-3292-4179-976f-2514195871fa", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "mercadoria", "operator": {"type": "string", "operation": "contains"}}, {"id": "6e694a8c-b424-4b78-add8-60dc97fb9750", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "saida", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "id": "8e9eb344-c6c2-45ee-af80-abe6f0d904a5", "name": "Cliente/Fornecedor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1320, 280]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "a17a390e-511f-4051-b561-01ed80604805", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "e43a9a98-3f29-480d-af8c-0c788c8f08be", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-740, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "a17a390e-511f-4051-b561-01ed80604805", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "21b8f2bb-24da-4537-b271-c287e9c45040", "name": "If1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-740, 500]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}, {"id": "22092507-61ee-41b6-bbed-940eee2c39cc", "name": "CODCNAE", "value": "1111-1/01", "type": "string"}, {"id": "177171da-3dbc-4c93-a96e-d84df0d8430c", "name": "EMAIL", "value": "<EMAIL>", "type": "string"}, {"id": "1ea4df0d-9102-48d1-abd1-572578ccdf84", "name": "CODCONTAB", "value": "20098230", "type": "string"}, {"id": "1f761949-ca49-4a5d-85c6-7969403d2e22", "name": "CODFUNCCADASTRO", "value": "1", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "5bedaf28-1fc4-42ee-960e-fd5a94e8e04b", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-1480, 280]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DISTINCT(APENASNUMEROS(CGCENT)) AS CGCENT\n  FROM PCCLIENT\n  WHERE APENASNUMEROS(CGCENT) = '{{ $('Validar Consumidor Final').item.json[\"CGCENT\"] }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-900, 200], "id": "05e59ab8-55d6-4661-8c60-87d14aaaf801", "name": "Validar Cadastro Cliente", "alwaysOutputData": false, "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT PROXNUMCLI FROM PCCONSUM", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-520, 80], "id": "e5d367f1-ecbb-4fb4-8b6b-5cd8f0da474a", "name": "Pegar o próximo codcli", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMCLI = PROXNUMCLI + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-140, 80], "id": "67c8e8bf-41dd-4f22-92ad-9b15c46b9107", "name": "Atualizar a sequencia", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DISTINCT (APENASNUMEROS(CGC)) AS CGC\n  FROM PCFORNEC\n  WHERE APENASNUMEROS(CGC) = '{{ $('Validar Consumidor Final').item.json[\"CGCENT\"] }}'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-900, 500], "id": "d8574d51-f687-4e4f-a522-68648d70893f", "name": "Validar Cadastro Fornecedor", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "select", "table": {"__rl": true, "value": "PCCONSUM", "mode": "list", "cachedResultName": "PCCONSUM"}, "returnAll": true, "options": {"outputColumns": ["PROXNUMFORNEC"]}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-520, 380], "id": "de8eacfe-8514-4782-8056-3bc093ac67cb", "name": "Pegar o próximo codfornec", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "BEGIN\n  UPDATE PCCONSUM\n     SET PROXNUMFORNEC = PROXNUMFORNEC + 1;\n  COMMIT;\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-140, 380], "id": "d9b6b720-4059-4e7d-a503-0bd8f53aff14", "name": "Atualizar a sequencia1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCCLIENT", "mode": "list", "cachedResultName": "PCCLIENT"}, "fields": {"string": [{"name": "CODCLI", "value": "={{ $json.PROXNUMCLI }}"}, {"name": "CGCENT", "value": "={{ $('Loop').item.json.CGCENT }}"}, {"name": "CLIENTE", "value": "={{ $('Loop').item.json.xNome }}"}, {"name": "FANTASIA", "value": "={{ $('Loop').item.json.xNome }}"}, {"name": "IEENT", "value": "={{ $('Loop').item.json.IE }}"}, {"name": "CODATV1", "value": "={{ $('Loop').item.json.CODATV1 }}"}, {"name": "ENDERENT", "value": "={{ $('Loop').item.json.xLgr }}"}, {"name": "NUMEROENT", "value": "={{ \n  $('Loop').item.json.nro\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')  // Remove acentos\n    .replace(/[^a-zA-Z0-9]/g, '')     // Remove caracteres especiais como '-'\n    .slice(0, 6) === \"semnu\" ? \"SN\" : $('Loop').item.json.nro\n    .replace(/[^a-zA-Z0-9]/g, '')   // Remove caracteres especiais\n}}"}, {"name": "COMPLEMENTOENT"}, {"name": "BAIRROENT", "value": "={{ $('Loop').item.json.xBairro }}"}, {"name": "TELENT"}, {"name": "ESTENT", "value": "={{ $('Loop').item.json.UF }}"}, {"name": "CEPENT", "value": "={{ $('Loop').item.json.CEP }}"}, {"name": "CODCIDADE", "value": "=(SELECT MIN(CODCIDADE) FROM PCCIDADE WHERE CODIBGE = {{ $('Loop').item.json.cMun }})"}, {"name": "CODMUNICIPIO", "value": "={{ $('Loop').item.json.cMun }}"}, {"name": "CODPAIS", "value": "={{ $('Loop').item.json.cPais }}"}, {"name": "PONTOREFER"}, {"name": "ENDERCOB", "value": "={{ $('Loop').item.json.xLgr }}"}, {"name": "COMPLEMENTOCOB"}, {"name": "NUMEROCOB", "value": "={{ \n  $('Loop').item.json.nro\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')  // Remove acentos\n    .replace(/[^a-zA-Z0-9]/g, '')     // Remove caracteres especiais como '-'\n    .slice(0, 6) === \"semnu\" ? \"SN\" : $('Loop').item.json.nro\n    .replace(/[^a-zA-Z0-9]/g, '')   // Remove caracteres especiais\n}}"}, {"name": "BAIRROCOB", "value": "={{ $('Loop').item.json.xBairro }}"}, {"name": "MUNICCOB", "value": "={{ $('Loop').item.json.xMun }}"}, {"name": "ESTCOB", "value": "={{ $('Loop').item.json.UF }}"}, {"name": "CEPCOB", "value": "={{ $('Loop').item.json.CEP }}"}, {"name": "ENDERCOM", "value": "={{ $('Loop').item.json.xLgr }}"}, {"name": "NUMEROCOM", "value": "={{ \n  $('Loop').item.json.nro\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')  // Remove acentos\n    .replace(/[^a-zA-Z0-9]/g, '')     // Remove caracteres especiais como '-'\n    .slice(0, 6) === \"semnu\" ? \"SN\" : $('Loop').item.json.nro\n    .replace(/[^a-zA-Z0-9]/g, '')   // Remove caracteres especiais\n}}"}, {"name": "COMPLEMENTOCOM"}, {"name": "BAIRROCOM", "value": "={{ $('Loop').item.json.xBairro }}"}, {"name": "MUNICCOM", "value": "={{ $('Loop').item.json.xMun }}"}, {"name": "ESTCOM", "value": "={{ $('Loop').item.json.UF }}"}, {"name": "CEPCOM", "value": "={{ $('Loop').item.json.CEP }}"}, {"name": "CODUSUR1", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "CODUSUR2"}, {"name": "CODPRACA", "value": "={{ $('Loop').item.json.CODPRACA }}"}, {"name": "PREDIOPROPRIO"}, {"name": "OBS"}, {"name": "OBSENTREGA1"}, {"name": "OBSENTREGA2"}, {"name": "OBSENTREGA3"}, {"name": "OBSENTREGA4"}, {"name": "NUMBANCO1"}, {"name": "NUMAGENCIA1"}, {"name": "NUMCCORRENTE1"}, {"name": "NUMBANCO2"}, {"name": "NUMAGENCIA2"}, {"name": "NUMCCORRENTE2"}, {"name": "OBSGERENCIAL1"}, {"name": "OBSGERENCIAL2"}, {"name": "OBSGERENCIAL3"}, {"name": "DTCADASTRO", "value": "SYSDATE"}, {"name": "DTULTALTER", "value": "SYSDATE"}, {"name": "CODCOB", "value": "={{ $('Loop').item.json.CODCOB }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "CODFILIALNF", "value": "1"}, {"name": "CONSUMIDORFINAL", "value": "={{ $('Loop').item.json.CONSUMIDORFINAL }}"}, {"name": "CONTRIBUINTE", "value": "={{ $('Loop').item.json.CONTRIBUIENTE }}"}, {"name": "CLIENTPROTESTO", "value": "S"}, {"name": "SIMPLESNACIONAL"}, {"name": "FRETEDESPACHO"}, {"name": "CALCULAST"}, {"name": "CLIENTEFONTEST"}, {"name": "PARTICIPAFUNCEP", "value": "S"}, {"name": "CGCENTREGA"}, {"name": "MUNICENT", "value": "={{ $('Loop').item.json.xMun }}"}, {"name": "TIPOFJ", "value": "={{ $('Loop').item.json.TIPOFJ }}"}, {"name": "CODCONTAB", "value": "={{ $('Loop').item.json.CODCONTAB }}"}, {"name": "CODFUNCCADASTRO", "value": "={{ $('Loop').item.json.CODFUNCCADASTRO }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-320, 80], "id": "b7607cab-855f-4a72-954c-dd8f9398a815", "name": "Cadastrar Cliente", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCFORNEC", "mode": "list", "cachedResultName": "PCFORNEC"}, "fields": {"string": [{"name": "CGC", "value": "={{ $('Loop1').item.json.CGCENT }}"}, {"name": "FORNECEDOR", "value": "={{ $('Loop1').item.json.xNome }}"}, {"name": "FANTASIA", "value": "={{ $('Loop1').item.json.xNome }}"}, {"name": "IE", "value": "={{ $('Loop1').item.json.IE }}"}, {"name": "ENDER", "value": "={{ $('Loop1').item.json.xLgr }}"}, {"name": "NUMEROEND", "value": "={{ $('Loop1').item.json.nro.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') === \"sem numero\" ? \"SN\" : $('Loop1').item.json.nro }}"}, {"name": "COMPLEMENTOEND"}, {"name": "BAIRRO", "value": "={{ $('Loop1').item.json.xBairro }}"}, {"name": "TELFAB"}, {"name": "ESTADO", "value": "={{ $('Loop1').item.json.UF }}"}, {"name": "CEP", "value": "={{ $('Loop1').item.json.CEP }}"}, {"name": "CODCIDADE", "value": "=(SELECT MIN(CODCIDADE) FROM PCCIDADE WHERE CODIBGE = {{ $('Loop1').item.json.cMun }})"}, {"name": "CODMUNICIPIO", "value": "={{ $('Loop1').item.json.cMun }}"}, {"name": "CODPAIS", "value": "={{ $('Loop1').item.json.cPais }}"}, {"name": "DTCADASTRO", "value": "SYSDATE"}, {"name": "DTULTALTER", "value": "SYSDATE"}, {"name": "CONSUMIDORFINAL", "value": "={{ $('Loop1').item.json.CONSUMIDORFINAL }}"}, {"name": "CODFORNEC", "value": "={{ $json.PROXNUMFORNEC }}"}, {"name": "MUNICOB", "value": "={{ $('Loop1').item.json.xMun }}"}, {"name": "TIPOPESSOA", "value": "={{ $('Loop1').item.json.TIPOFJ }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-320, 380], "id": "f47c67c6-9b3d-4374-846f-0b3a7c53bd68", "name": "Cadastra<PERSON>", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"options": {}}, "id": "5dc9b7b8-bc11-45b8-ad8a-a7150240c47f", "name": "Loop", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1120, 120]}, {"parameters": {"options": {}}, "id": "16ec0631-b16f-4d49-91e8-837c2b10900e", "name": "Loop1", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1120, 420]}, {"parameters": {"fileSelector": "//*************/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-2280, 280], "id": "fc8109ca-1e50-48ea-b555-b97464f458b0", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-2120, 280], "id": "358ff972-26c8-4937-a142-c10332922e7f", "name": "Extract from File"}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Validar Consumidor Final", "type": "main", "index": 0}]]}, "Validar Consumidor Final": {"main": [[{"node": "Remove Underfined", "type": "main", "index": 0}]]}, "Remove Underfined": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Cliente/Fornecedor": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Pegar o próximo codcli", "type": "main", "index": 0}], [{"node": "Loop", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Pegar o próximo codfornec", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Cliente/Fornecedor", "type": "main", "index": 0}]]}, "Validar Cadastro Cliente": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Pegar o próximo codcli": {"main": [[{"node": "Cadastrar Cliente", "type": "main", "index": 0}]]}, "Atualizar a sequencia": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Validar Cadastro Fornecedor": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Pegar o próximo codfornec": {"main": [[{"node": "Cadastra<PERSON>", "type": "main", "index": 0}]]}, "Atualizar a sequencia1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "Cadastrar Cliente": {"main": [[{"node": "Atualizar a sequencia", "type": "main", "index": 0}]]}, "Cadastrar Fornecedor": {"main": [[{"node": "Atualizar a sequencia1", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "Validar Cadastro Cliente", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Validar Cadastro Fornecedor", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}