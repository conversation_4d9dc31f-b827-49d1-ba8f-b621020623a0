{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [140, 40], "id": "ac0a3a0e-ab33-40fd-a78a-2dc306da4f00", "name": "When clicking ‘Test workflow’"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [680, 40], "id": "7bd36f13-08e2-4c90-b0b4-2c69f52856a6", "name": "XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [500, 40], "id": "3411e3aa-9d87-4e67-9054-20da4f6ba3b6", "name": "Extract from File"}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [320, 40], "id": "8b8a0c11-95a6-42f7-a3cd-99a33e1987c7", "name": "Ler XML"}, {"parameters": {"jsCode": "// 3-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n\n    for (const det of detList) {\n      if (det['prod']) {\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],        \n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 40], "id": "f72a8401-e332-4612-a393-5505844969fc", "name": "Extrair dados"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1decf10e-23f2-4307-b386-97fa96f46cb3", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1040, 40], "id": "9d2b0b59-b88c-4a63-989a-e9bcf0d581d5", "name": "Entrada/Saida"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT NUMTRANSVENDA AS NUM_TRANSACAO,\n       'S' AS TIPO,\n       DTCANCEL\n  FROM PCNFSAID\n  WHERE CHAVENFE = '{{ $json.chNFe }}'\n    AND EXISTS (SELECT NUMTRANSVENDA\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = PCNFSAID.NUMTRANSVENDA\n          AND ROWNUM = 1)\n  UNION ALL\nSELECT NUMTRANSENT AS NUM_TRANSACAO,\n       'E' AS TIPO,\n       DTCANCEL\n  FROM PCNFENT\n  WHERE CHAVENFE = '{{ $json.chNFe }}'\n    AND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = PCNFENT.NUMTRANSENT\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1260, -40], "id": "a19f041e-4685-429d-b994-e22bab04348f", "name": "NUM_TRANSACAO", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "c69936ca-3699-43ec-a675-55329d08fc9b", "leftValue": "={{ $json.not_found }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "058e4c04-10c4-43be-bc15-3b037d9824d3", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1440, -40]}, {"parameters": {"fields": {"values": [{"name": "fileName", "stringValue": "={{ $('Ler XML').item.json.fileName }}"}]}, "include": "none", "options": {}}, "id": "a68c41d8-d5fa-45ad-993b-5122eb838d6d", "name": "File Name", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [1640, 40]}, {"parameters": {}, "id": "31f6875f-44cb-432b-9545-8718b3df0864", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [1820, 40]}, {"parameters": {"protocol": "sftp", "operation": "delete", "path": "=/u01/NERVSFLOW/PROCESSADOS/{{ $('Entrada/Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('') }}/{{ $json[\"fileName\"] }}", "options": {}}, "id": "6e55c565-d64f-4c9e-845d-b4d234c4eb06", "name": "Delete XML1", "type": "n8n-nodes-base.ftp", "typeVersion": 1, "position": [2420, 220], "credentials": {"sftp": {"id": "av04Vw8ECtsPZXbs", "name": "NERVSFLOW"}}, "onError": "continueErrorOutput"}, {"parameters": {"protocol": "sftp", "operation": "rename", "oldPath": "=/u01/NERVSFLOW/{{ $json[\"fileName\"] }}", "newPath": "=/u01/NERVSFLOW/PROCESSADOS/{{ $('Entrada/Saida').item.json.dhEmi.split('T')[0].split('-').reverse().join('') }}/{{ $json[\"fileName\"] }}", "options": {"createDirectories": true}}, "id": "07a49633-e1ea-4124-b80b-ac84a5df5035", "name": "Mover XML", "type": "n8n-nodes-base.ftp", "typeVersion": 1, "position": [2000, 40], "credentials": {"sftp": {"id": "av04Vw8ECtsPZXbs", "name": "NERVSFLOW"}}, "onError": "continueErrorOutput"}, {"parameters": {"protocol": "sftp", "operation": "delete", "path": "={{ $json[\"error\"].match(/To: (.+)$/)[1] }}", "options": {}}, "id": "7c637fc4-8c52-4f07-9fe1-5c5d506a12c7", "name": "Delete XML", "type": "n8n-nodes-base.ftp", "typeVersion": 1, "position": [2420, 40], "credentials": {"sftp": {"id": "av04Vw8ECtsPZXbs", "name": "NERVSFLOW"}}, "onError": "continueErrorOutput"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.error.toLowerCase() }}", "rightValue": "rename", "operator": {"type": "string", "operation": "contains"}, "id": "9c606254-14a7-4872-b447-dfc0ee39b7cb"}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "4751bc72-4d7c-44e3-83ae-7f99c87438eb", "leftValue": "={{ $json.error.toLowerCase() }}", "rightValue": "internal error", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Internal Error"}]}, "options": {}}, "id": "97e8ee32-ba16-42fb-9b66-654e1738c49c", "name": "Switch1", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [2220, 120]}], "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Entrada/Saida", "type": "main", "index": 0}]]}, "Entrada/Saida": {"main": [[{"node": "NUM_TRANSACAO", "type": "main", "index": 0}], []]}, "NUM_TRANSACAO": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[], [{"node": "File Name", "type": "main", "index": 0}]]}, "File Name": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Mover XML", "type": "main", "index": 0}]]}, "Delete XML1": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "Mover XML": {"main": [[], [{"node": "Switch1", "type": "main", "index": 0}]]}, "Delete XML": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Delete XML", "type": "main", "index": 0}], [{"node": "Delete XML1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}