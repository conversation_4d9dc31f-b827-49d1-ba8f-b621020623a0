{"nodes": [{"parameters": {}, "id": "4d1ac824-d900-4787-9a64-1332b8e72194", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1400, 940]}, {"parameters": {"options": {}}, "id": "9153953e-c427-420a-87f0-023c69ab0f6b", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-700, 960]}, {"parameters": {"jsCode": "// 5-Impostos e Estoque\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'])\n    const vFreteTotal = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0)\n    // Obter a natureza da operação já utilizada para resultItem\n    const natOp = xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp']\n    // Lista de CFOPs de devolução\n    const devolucaoCFOPs = [\n      '1201',\n      '1202',\n      '1203',\n      '1204',\n      '1208',\n      '1209',\n      '1212',\n      '1213',\n      '1214',\n      '1215',\n      '1216',\n      '1410',\n      '1411',\n      '1503',\n      '1504',\n      '1505',\n      '1506',\n      '1553',\n      '1660',\n      '1661',\n      '1662',\n      '1918',\n      '1919',\n      '2201',\n      '2202',\n      '2203',\n      '2204',\n      '2208',\n      '2209',\n      '2212',\n      '2213',\n      '2214',\n      '2215',\n      '2216',\n      '2410',\n      '2411',\n      '2503',\n      '2504',\n      '2505',\n      '2506',\n      '2553',\n      '2660',\n      '2661',\n      '2662',\n      '2918',\n      '2919',\n      '3201',\n      '3202',\n      '3211',\n      '3212',\n      '3503',\n      '3553',\n      '5201',\n      '5202',\n      '5208',\n      '5209',\n      '5210',\n      '5213',\n      '5214',\n      '5215',\n      '5216',\n      '5410',\n      '5411',\n      '5412',\n      '5413',\n      '5503',\n      '5553',\n      '5555',\n      '5556',\n      '5660',\n      '5661',\n      '5662',\n      '5918',\n      '5919',\n      '5921',\n      '6201',\n      '6202',\n      '6208',\n      '6209',\n      '6210',\n      '6213',\n      '6214',\n      '6215',\n      '6216',\n      '6410',\n      '6411',\n      '6412',\n      '6413',\n      '6503',\n      '6553',\n      '6555',\n      '6556',\n      '6660',\n      '6661',\n      '6662',\n      '6918',\n      '6919',\n      '6921',\n      '7201',\n      '7202',\n      '7210',\n      '7211',\n      '7212',\n      '7553',\n      '7556',\n      '7930'\n    ]\n\n    for (const det of detList) {\n      if (det['prod']) {\n        const nItem = det['nItem']\n        const produto = det['prod']\n        const imposto = det['imposto']\n        // Adicionando o campo vIPIDevol para devolução\n        const vIPIDevol = ICMSTot['vIPIDevol']\n          ? parseFloat(ICMSTot['vIPIDevol']) / parseFloat(produto['qCom'])\n          : 0\n        // Correção no acesso às variáveis PIS e COFINS\n        const pis =\n          imposto &&\n          imposto['PIS'] &&\n          (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr'])\n        const cofins =\n          imposto &&\n          imposto['COFINS'] &&\n          (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr'])\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const vBCUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vBCUFDest']) ||\n          0\n        const pICMSUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSUFDest']) ||\n          0\n        const pICMSInter =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInter']) ||\n          0\n        const pICMSInterPart =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInterPart']) ||\n          0\n        const pFCPUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pFCPUFDest']) ||\n          0\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n\n        const vIPIItem = ipi\n          ? parseFloat(ipi['vIPI'] / parseFloat(produto['qCom']) || 0)\n          : 0\n        const vDescItem =\n          parseFloat(produto['vDesc'] / parseFloat(produto['qCom'])) || 0\n        const vFreteItem = produto['vFrete']\n          ? parseFloat(\n              (produto['vFrete'] / parseFloat(produto['qCom'])).toFixed(6)\n            )\n          : 0\n        // Verificar se é devolução\n        const isDevolucao = devolucaoCFOPs.includes(produto['CFOP'])\n        const PUNITCONT = parseFloat(\n          (\n            parseFloat(produto['vUnCom']) +\n            (isDevolucao ? vIPIDevol : vIPIItem) -\n            vDescItem\n          ).toFixed(3)\n        )\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n\n        // Calcular os valores conforme as fórmulas fornecidas\n        const vICMSSubstituto = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSSubstituto']) || 0) / produto['qCom']\n        )\n\n        const vBCST = parseFloat(\n          ((icms && icms[icmsObj]?.['vBCST']) || 0) / produto['qCom']\n        )\n\n        const vICMSST = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSST']) || 0) / produto['qCom']\n        )\n\n        const pICMSST = parseFloat((icms && icms[icmsObj]?.['pICMSST']) || 0)\n\n        const pMVAST = parseFloat((icms && icms[icmsObj]?.['pMVAST']) || 0)\n\n        // BC de IPI  = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        const VLICMSPARTDEST = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFDest']) ||\n            0) / produto['qCom']\n        )\n\n        const VLICMSPARTREM = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFRemet']) ||\n            0) / produto['qCom']\n        )\n\n        const VLFCPPART = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vFCPUFDest']) ||\n            0) / produto['qCom']\n        )\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis\n          ? pis['CST'] ||\n            pis['PISAliq']?.['CST'] ||\n            pis['PISOutr']?.['CST'] ||\n            '00'\n          : cofins\n          ? cofins['CST'] ||\n            cofins['COFINSAliq']?.['CST'] ||\n            cofins['COFINSOutr']?.['CST'] ||\n            '00'\n          : '00'\n        const xPed = produto['xPed'] || ''\n        // Adicione o resultado ao array 'results'\n        const pICMS = icms ? parseFloat(icms[icmsObj]?.['pICMS']) : 0\n        const resultItem = {\n          nItem: nItem,\n          cProd: produto['cProd'],\n          xProd: produto['xProd'],\n          ncm: produto['NCM'],\n          CODCEST: produto['CEST'] || '',\n          cEAN: produto['cEAN'],\n          uCom: produto['uCom'],\n          qCom: produto['qCom'],\n          vUnCom: produto['vUnCom'],\n          vProd: produto['vProd'],\n          vProdTotal: vProdTotal,\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          natOp,\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vOutro: vOutro,\n          vFreteItem: vFreteItem,\n          vFreteTotal: vFreteTotal,\n          vDesc:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vDesc'],\n          vDescItem: vDescItem,\n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          ufDest:\n            xmlData['nfeProc']['NFe']['infNFe']['dest']['enderDest']['UF'],\n          ufEmit:\n            xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          pPIS: pis ? pis['pPIS'] || 0 : 0,\n          vPIS: pis ? parseFloat(pis['vPIS'] / produto['qCom']) || 0 : 0,\n          pCOFINS: cofins ? cofins['pCOFINS'] || 0 : 0,\n          vCOFINS: cofins\n            ? parseFloat(cofins['vCOFINS'] / produto['qCom']) || 0\n            : 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          // não utilizar este calculo\n          // vIPI: parseFloat(\n          //   (\n          //     produto['vProd'] *\n          //     (parseFloat(ipi ? ipi['pIPI'] || 0 : 0) / 100)\n          //   ).toFixed(2)\n          // ),\n          vIPIItem,\n          CODSITTRIBIPI: ipi ? ipi['CST'] || 0 : 0,\n          //VLBASEIPI items\n          vBC_IPI: imposto?.['IPI']?.['IPITrib']?.['vBC']\n            ? parseFloat(imposto['IPI']['IPITrib']['vBC'] / produto['qCom'])\n            : 0,\n          vBC_PIS: pis ? pis['vBC'] || 0 : 0,\n          vBC_COFINS: cofins ? cofins['vBC'] || 0 : 0,\n          /* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          vBC_ICMS: icms\n            ? parseFloat(\n                (\n                  icms[icmsObj]?.['vBC'] / produto['qCom'] -\n                  vFreteItem -\n                  vOutro / produto['qCom']\n                ).toFixed(2)\n              ) || 0\n            : 0,\n          vICMS: parseFloat(icms && icms[icmsObj]?.['vICMS']) || 0,\n          pICMS: pICMS,\n          CST: icms ? parseFloat(icms[icmsObj]?.['CST']) || '00' : '00',\n          CST_PIS_COFINS, // Variável única para CST\n          BASEICMS: BASEICMS,\n          STBCR: parseFloat(\n            ((icms && icms[icmsObj]?.['vICMSSTRet']) || 0) / produto['qCom']\n          ),\n          VLICMSBCR: vICMSSubstituto,\n          BASEICST: vBCST,\n          pST: icms ? parseFloat(icms[icmsObj]?.['pST']) || 0 : 0,\n          ST: vICMSST,\n          PERCST: pICMSST,\n          IVA: pMVAST,\n          VLBASEIPI: VLBASEIPI || 0,\n          vBCUFDest: vBCUFDest,\n          pICMSUFDest: pICMSUFDest,\n          pICMSInter: pICMSInter,\n          pICMSInterPart: pICMSInterPart,\n          VLICMSPARTDEST: VLICMSPARTDEST,\n          VLICMSPARTREM: VLICMSPARTREM,\n          pFCPUFDest: pFCPUFDest,\n          VLFCPPART: VLFCPPART,\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          PUNITCONT: PUNITCONT,\n          VLBASEPISCOFINS: isDevolucao\n            ? imposto?.['PIS']?.['PISOutr']?.['vBC']\n              ? parseFloat(imposto['PIS']['PISOutr']['vBC'])\n              : imposto?.['COFINS']?.['COFINSOutr']?.['vBC']\n              ? parseFloat(imposto['COFINS']['COFINSOutr']['vBC'])\n              : 0\n            : imposto?.['PIS']?.['PISAliq']?.['vBC']\n            ? parseFloat(imposto['PIS']['PISAliq']['vBC'])\n            : imposto?.['COFINS']?.['COFINSAliq']?.['vBC']\n            ? parseFloat(imposto['COFINS']['COFINSAliq']['vBC'])\n            : 0,\n          xPed,\n          vIPIDevol\n        }\n\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "fbdd350c-0041-45b8-9ff9-84d9f7376578", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-540, 960], "alwaysOutputData": false}, {"parameters": {"content": "## PATH BASE TESTE\n//*************/xml_mimo/TESTE/*.xml", "height": 99, "width": 351, "color": 4}, "id": "21e7c0f1-e16e-4a74-aefd-25e16896659e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1380, 820]}, {"parameters": {"content": "## Notas de Entrada\n- Campo: **CALCCREDIPI = N** - Grava o **VLIPI** em campo **vl.Outras IPI** no livro fiscal", "height": 80, "width": 577, "color": 4}, "id": "17125586-3cae-4f21-81e0-29ea6ccf9276", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [240, 420]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "c3dd8f8b-35f9-4b81-a901-49fcf96eebde", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-380, 960]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "entrada", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "cc368211-53f0-4ca7-b6c2-6317e4a4b232", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-40, 940]}, {"parameters": {"content": "## Notas de Devolução\n- Campo: **CALCCREDIPI = S** - Grava o **VLIPI** em campo próprio no livro fiscal\n- Acesse a **rotina 132** e marque o parâmetro **GERAINFFISCAISDTENTREGADTSAIDA = S** para que as devoluções apareçam na **rotina 1001**", "height": 129, "width": 617, "color": 5}, "id": "0ffea986-a9d6-4f31-a44c-14f0e9d49be9", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [260, 1000]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ee4ae02c-b9c4-41ae-9a71-6a1e95656634", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [420, 520], "id": "d3a6031c-0ffd-4b39-901f-c13fe12f955c", "name": "If"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [640, 540], "id": "7876d886-afe6-44bd-854b-b4049c258de4", "name": "Loop"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [840, 560], "id": "f6932d85-b55d-4da0-b7db-6179968cd6b7", "name": "NUMTRANSITEM", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "fields": {"string": [{}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1200, 560], "id": "12dbbddc-fce3-4032-8cf9-63e26a9ad667", "name": "PCMOV", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "list", "cachedResultName": "PCMOVCOMPLE"}, "fields": {"string": [{}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1380, 560], "id": "fc3593e2-722a-452c-81bb-9e188ac3d963", "name": "PCMOVCOMPLE", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSENT\n  FROM PCNFENT N\n WHERE CHAVENFE = '{{ $json.chNFe }}'\n   AND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = N.NUMTRANSENT\n          AND TRUNC(DTMOV) < TRUNC(DTMOVLOG)\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [460, 760], "id": "64391c7d-3f4f-4557-ab77-116f2e538cec", "name": "Valida PCMOV", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.CUSTOFIN,\n           E.CUSTOREAL,\n           E.CUSTOREP,\n           E.CUSTOCONT,\n           ((({{ $('natOp').item.json.vUnCom }} * C.CODICMTAB) / 100) + (({{ $('natOp').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $('Loop').item.json.CODFILIAL }}'\n        AND T.UFDESTINO = '{{ $('natOp').item.json.ufDest }}'\n        AND E.CODPROD = {{ $('natOp').item.json.cProd }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $('natOp').item.json.vUnCom }} * T.CODICMTAB) / 100) + (({{ $('natOp').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $('natOp').item.json.ufDest }}'\n    AND E.CODPROD = {{ $('natOp').item.json.cProd }}\n    AND E.CODFILIAL = '{{ $('Loop').item.json.CODFILIAL }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1020, 560], "id": "44e6088e-a53a-4b36-9c43-f1ebfaf1e3c7", "name": "CUSTOS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "where": {"values": [{"column": "NUMTRANSENT", "value": "={{ $('If1').item.json.NUMTRANSENT }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [860, 780], "id": "7b5399f0-d696-40b7-bbbf-51a8a8e3a997", "name": "Corrigir DTMOVLOG", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ee4ae02c-b9c4-41ae-9a71-6a1e95656634", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [440, 1140], "id": "2cc9c07e-3d77-4fce-871b-ec0511f5f63a", "name": "If2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [660, 1160], "id": "20592e7c-16a1-45a6-99dc-6c72f17c80da", "name": "Loop2"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [840, 1180], "id": "c0c771dd-40fc-424e-a0c7-9cba1481ac96", "name": "NUMTRANSITEM1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "list", "cachedResultName": "PCMOVCOMPLE"}, "fields": {"string": [{}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1380, 1180], "id": "603299a8-0c53-41c6-9463-80b2d98c53b7", "name": "PCMOVCOMPLE1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSENT\n  FROM PCNFENT N\n WHERE CHAVENFE = '{{ $json.chNFe }}'\n  AND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = N.NUMTRANSENT\n          AND TRUNC(DTMOV) < TRUNC(DTMOVLOG)\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [460, 1380], "id": "74d0d2d7-6c79-4bb2-b653-3adcbe649608", "name": "Valida PCMOV1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5005be8c-c598-4d07-a6f2-7691b277d654", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [640, 1380], "id": "2722d9bf-2b99-4415-96e8-d2631bea0dcd", "name": "If3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.CUSTOFIN,\n           E.CUSTOREAL,\n           E.CUSTOREP,\n           E.CUSTOCONT,\n           ((({{ $('natOp').item.json.vUnCom }} * C.CODICMTAB) / 100) + (({{ $('natOp').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $('Loop2').item.json.CODFILIAL }}'\n        AND T.UFDESTINO = '{{ $('natOp').item.json.ufDest }}'\n        AND E.CODPROD = {{ $('natOp').item.json.cProd }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $('natOp').item.json.vUnCom }} * T.CODICMTAB) / 100) + (({{ $('natOp').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $('natOp').item.json.ufDest }}'\n    AND E.CODPROD = {{ $('natOp').item.json.cProd }}\n    AND E.CODFILIAL = '{{ $('Loop2').item.json.CODFILIAL }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1020, 1180], "id": "2f7bedd4-d1e5-4116-b192-dd687c46ee48", "name": "CUSTOS1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "where": {"values": [{"column": "NUMTRANSENT", "value": "={{ $('If3').item.json.NUMTRANSENT }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [860, 1400], "id": "6cf6a51f-f325-4d40-8f5f-d74a55a47d09", "name": "Corrigir DTMOVLOG1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [260, 760], "id": "ea3fb4e4-e6fa-46e3-aaa7-48a76bfa7dc7", "name": "Remove Duplicates"}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [260, 1380], "id": "75564144-ffbd-4a7c-9dc2-318c742a95b7", "name": "Remove Duplicates1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5005be8c-c598-4d07-a6f2-7691b277d654", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [640, 760], "id": "ddef61ef-fd9c-4939-ba44-913fb98aebcb", "name": "If1"}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "fields": {"string": [{}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1200, 1180], "id": "0e347659-2a77-410e-be3f-df90d80a67a5", "name": "PCMOV1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"fileSelector": "//*************/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-1240, 940], "id": "b39c641f-fc54-4bfb-8586-c82472de835f", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-860, 960], "id": "c8ea2996-707c-4a3e-b793-86dca0347679", "name": "Extract from File"}, {"parameters": {"content": "## Integrado relatórios\n- **Rotinas: (1001, 1013, 1022, 1072, 1096)**", "height": 120, "width": 360, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [900, 1000], "typeVersion": 1, "id": "ac77e9e6-54c6-4985-98d5-226e382f6c08", "name": "Sticky Note3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "0", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "68b21713-dd72-4b1b-966e-5978a990a0d9", "name": "Entrada", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-220, 960]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1060, 940], "id": "9bf28828-83f1-4ea1-8b35-45445d142a62", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT E.NUMTRANSENT, \n       <PERSON><PERSON>, \n       <PERSON>.CODFIL<PERSON>L,\n       E.C<PERSON>,\n      (SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"CGCFILIAL\"] }}') AS CODCLI,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"CGCFILIAL\"] }}') AS CODFORNEC,\n      CASE WHEN E.TIPODESCARGA = 'R' THEN 'ER' WHEN E.TIPODESCARGA = '6' THEN 'ED' END CODOPER,\n      CASE WHEN TIPODESCARGA = '6' THEN '{{ $json[\"CODDEVOL\"] }}' ELSE NULL END CODDEVOL\n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT NUMTRANSENT FROM PCMOV WHERE NUMTRANSENT = E.NUMTRANSENT AND ROWNUM = 1)\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [240, 520], "id": "f8620811-a58c-44bb-9da7-b122ce1b2d1a", "name": "Valida Entrada", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT E.NUMTRANSENT, \n       <PERSON><PERSON>, \n       <PERSON>.CODFIL<PERSON>L,\n       E.C<PERSON>,\n      (SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"CGCFILIAL\"] }}') AS CODCLI,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"CGCFILIAL\"] }}') AS CODFORNEC,\n      CASE WHEN E.TIPODESCARGA = 'R' THEN 'ER' WHEN E.TIPODESCARGA = '6' THEN 'ED' END CODOPER,\n      CASE WHEN TIPODESCARGA = '6' THEN '{{ $json[\"CODDEVOL\"] }}' ELSE NULL END CODDEVOL\n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT NUMTRANSENT FROM PCMOV WHERE NUMTRANSENT = E.NUMTRANSENT AND ROWNUM = 1)\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [260, 1140], "id": "79357260-1285-4280-8dbe-6017210b28d7", "name": "Valida Devolução", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Entrada", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}, {"node": "Valida Entrada", "type": "main", "index": 0}], [{"node": "Remove Duplicates1", "type": "main", "index": 0}, {"node": "Valida Devolução", "type": "main", "index": 0}]]}, "If": {"main": [[], [{"node": "Loop", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "NUMTRANSITEM", "type": "main", "index": 0}]]}, "NUMTRANSITEM": {"main": [[{"node": "CUSTOS", "type": "main", "index": 0}]]}, "PCMOV": {"main": [[{"node": "PCMOVCOMPLE", "type": "main", "index": 0}]]}, "PCMOVCOMPLE": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Valida PCMOV": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "CUSTOS": {"main": [[{"node": "PCMOV", "type": "main", "index": 0}]]}, "Corrigir DTMOVLOG": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "If2": {"main": [[], [{"node": "Loop2", "type": "main", "index": 0}]]}, "Loop2": {"main": [[], [{"node": "NUMTRANSITEM1", "type": "main", "index": 0}]]}, "NUMTRANSITEM1": {"main": [[{"node": "CUSTOS1", "type": "main", "index": 0}]]}, "PCMOVCOMPLE1": {"main": [[{"node": "Loop2", "type": "main", "index": 0}]]}, "Valida PCMOV1": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "If3": {"main": [[], [{"node": "Corrigir DTMOVLOG1", "type": "main", "index": 0}]]}, "CUSTOS1": {"main": [[{"node": "PCMOV1", "type": "main", "index": 0}]]}, "Corrigir DTMOVLOG1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Valida PCMOV", "type": "main", "index": 0}]]}, "Remove Duplicates1": {"main": [[{"node": "Valida PCMOV1", "type": "main", "index": 0}]]}, "If1": {"main": [[], [{"node": "Corrigir DTMOVLOG", "type": "main", "index": 0}]]}, "PCMOV1": {"main": [[{"node": "PCMOVCOMPLE1", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "Entrada": {"main": [[{"node": "natOp", "type": "main", "index": 0}], []]}, "Fila": {"main": [[], [{"node": "Extract from File", "type": "main", "index": 0}]]}, "Valida Entrada": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Valida Devolução": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}