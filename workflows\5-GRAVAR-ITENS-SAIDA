{"nodes": [{"parameters": {}, "id": "cedf5029-3dff-47e4-8f7b-67bccc98a480", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1400, 960]}, {"parameters": {"options": {}}, "id": "e9d494a0-e69f-4f74-8a4e-033ed34c14d7", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-920, 960]}, {"parameters": {"jsCode": "// 5-Impostos e Estoque\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'])\n    const vFreteTotal = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0)\n\n    for (const det of detList) {\n      if (det['prod']) {\n        const nItem = det['nItem']\n        const produto = det['prod']\n        const imposto = det['imposto']\n        // Correção no acesso às variáveis PIS e COFINS\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const vBCUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vBCUFDest']) ||\n          0\n        const pICMSUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSUFDest']) ||\n          0\n        const pICMSInter =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInter']) ||\n          0\n        const pICMSInterPart =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInterPart']) ||\n          0\n        const pFCPUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pFCPUFDest']) ||\n          0\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n\n        const vIPIItem = ipi\n          ? parseFloat(ipi['vIPI'] / parseFloat(produto['qCom']) || 0)\n          : 0\n        const vDescItem = parseFloat(produto['vDesc'] / parseFloat(produto['qCom'])) || 0\n        const vFreteItem = produto['vFrete'] ? parseFloat((produto['vFrete'] / parseFloat(produto['qCom'])).toFixed(6)) : 0;\n        const PUNITCONT = parseFloat(\n          (parseFloat(produto['vUnCom']) + vIPIItem - vDescItem).toFixed(3)\n        )\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n\n        // Calcular os valores conforme as fórmulas fornecidas\n        const vICMSSubstituto = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSSubstituto']) || 0) / produto['qCom']\n        )\n\n        const vBCST = parseFloat(\n          ((icms && icms[icmsObj]?.['vBCST']) || 0) / produto['qCom']\n        )\n\n        const vICMSST = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSST']) || 0) / produto['qCom']\n        )\n\n        const pICMSST = parseFloat((icms && icms[icmsObj]?.['pICMSST']) || 0)\n\n        const pMVAST = parseFloat((icms && icms[icmsObj]?.['pMVAST']) || 0)\n\n        // BC de IPI  = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        const VLICMSPARTDEST = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFDest']) ||\n            0) / produto['qCom']\n        )\n\n        const VLICMSPARTREM = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFRemet']) ||\n            0) / produto['qCom']\n        )\n\n        const VLFCPPART = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vFCPUFDest']) ||\n            0) / produto['qCom']\n        )\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        const xPed = produto['xPed'] || '';\n        // Adicione o resultado ao array 'results'\n        const pICMS = icms ? parseFloat(icms[icmsObj]?.['pICMS']) : 0;\n        const resultItem = {\n          nItem: nItem,\n          cProd: produto['cProd'],\n          xProd: produto['xProd'],\n          ncm: produto['NCM'],\n          CODCEST: produto['CEST'] || '',\n          cEAN: produto['cEAN'],\n          uCom: produto['uCom'],\n          qCom: produto['qCom'],\n          vUnCom: produto['vUnCom'],\n          vProd: produto['vProd'],\n          vProdTotal: vProdTotal,\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vOutro: vOutro,\n          vFreteItem: vFreteItem,\n          vFreteTotal: vFreteTotal,\n          vDesc:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vDesc'],\n          vDescItem: vDescItem,\n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          ufDest:\n            xmlData['nfeProc']['NFe']['infNFe']['dest']['enderDest']['UF'],\n          ufEmit:\n            xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          pPIS: pis ? pis['pPIS'] || 0 : 0,\n          vPIS: pis ? parseFloat(pis['vPIS'] / produto['qCom']) || 0 : 0,\n          pCOFINS: cofins ? cofins['pCOFINS'] || 0 : 0,\n          vCOFINS: cofins\n            ? parseFloat(cofins['vCOFINS'] / produto['qCom']) || 0\n            : 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          // não utilizar este calculo\n          vIPI: parseFloat(\n            (\n              produto['vProd'] *\n              (parseFloat(ipi ? ipi['pIPI'] || 0 : 0) / 100)\n            ).toFixed(2)\n          ),\n          vIPIItem,\n          CODSITTRIBIPI: ipi ? ipi['CST'] || 0 : 0,\n          //VLBASEIPI items\n          vBC_IPI: imposto?.['IPI']?.['IPITrib']?.['vBC'] \n            ? parseFloat(imposto['IPI']['IPITrib']['vBC'] / produto['qCom'])\n            : 0,\n          vBC_PIS: pis ? pis['vBC'] || 0 : 0,\n          vBC_COFINS: cofins ? cofins['vBC'] || 0 : 0,\n          /* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          vBC_ICMS: icms\n            ? parseFloat(\n                (\n                  icms[icmsObj]?.['vBC'] / produto['qCom'] -\n                  vFreteItem -\n                  vOutro / produto['qCom']\n                ).toFixed(2)\n              ) || 0\n            : 0,\n          vICMS: parseFloat(icms && icms[icmsObj]?.['vICMS']) || 0,\n          pICMS: pICMS,\n          CST: icms ? parseFloat(icms[icmsObj]?.['CST']) || '00' : '00',\n          CST_PIS_COFINS, // Variável única para CST\n          BASEICMS: BASEICMS,\n          STBCR: parseFloat(\n            ((icms && icms[icmsObj]?.['vICMSSTRet']) || 0) / produto['qCom']\n          ),\n          VLICMSBCR: vICMSSubstituto,\n          BASEICST: vBCST,\n          pST: icms ? parseFloat(icms[icmsObj]?.['pST']) || 0 : 0,\n          ST: vICMSST,\n          PERCST: pICMSST,\n          IVA: pMVAST,\n          VLBASEIPI: VLBASEIPI || 0,\n          vBCUFDest: vBCUFDest,\n          pICMSUFDest: pICMSUFDest,\n          pICMSInter: pICMSInter,\n          pICMSInterPart: pICMSInterPart,\n          VLICMSPARTDEST: VLICMSPARTDEST,\n          VLICMSPARTREM: VLICMSPARTREM,\n          pFCPUFDest: pFCPUFDest,\n          VLFCPPART: VLFCPPART,\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          PUNITCONT: PUNITCONT,\n          VLBASEPISCOFINS: imposto?.['PIS']?.['PISAliq']?.['vBC'] \n            ? parseFloat(imposto['PIS']['PISAliq']['vBC'])\n            : imposto?.['COFINS']?.['COFINSAliq']?.['vBC']\n            ? parseFloat(imposto['COFINS']['COFINSAliq']['vBC'])\n            : 0,\n          xPed,\n        }\n\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results"}, "id": "c6643dec-3692-4139-bb01-f8576656b8dc", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-760, 960], "alwaysOutputData": false}, {"parameters": {"content": "## PATH BASE TESTE\n//192.168.1.245/xml_mimo/TESTE/*.xml", "height": 99, "width": 351, "color": 4}, "id": "0a4b3306-ca71-45f6-8fdf-c5f40ba40a12", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1380, 820]}, {"parameters": {"content": "## Not<PERSON> de Vendas", "height": 80, "width": 237, "color": 2}, "id": "93f0b0f1-769f-4d8c-b304-4a4da058c715", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [40, 420]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "16e361c2-95c8-40aa-a3f9-e37cb9b0696c", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-600, 960]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "venda", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "d31d0385-b906-4ad8-96c2-e5dd0dba2b05", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-260, 940]}, {"parameters": {"content": "## <PERSON><PERSON> Saídas - Simples Remessa - 1322\n- Cancelar via rotina 1326", "height": 89, "width": 497, "color": 5}, "id": "3fb3a19b-7355-4327-ab9d-b0439b2b9a68", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [20, 1020]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "25476545-09af-40e7-8b30-56734da51c31", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-440, 960]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT S.CODCLI,\n       S.<PERSON>SVENDA,\n       S.CODFILIAL,\n       CASE WHEN TIPOVENDA = '1' THEN 'S' \n            WHEN TIPOVENDA = 'SR' THEN 'SR' \n        END CODOPER\n  FROM PCNFSAID S\n  WHERE S.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n    AND S.DTCANCEL IS NULL\n    AND NOT EXISTS (SELECT NUMTRANSVENDA FROM PCMOV WHERE NUMTRANSVENDA = S.NUMTRANSVENDA AND ROWNUM = 1)\n    AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [240, 540], "id": "b9e1b40f-5f9c-4d0b-aff3-897e7ea57ae2", "name": "NUMTRANSVENDA", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ee4ae02c-b9c4-41ae-9a71-6a1e95656634", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [420, 540], "id": "1f22bd5a-18f4-465c-ac1b-14559af80bd0", "name": "If"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [40, 520], "id": "0bcbb798-375f-4399-afa9-8e761362993b", "name": "Loop"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [620, 560], "id": "c732371d-b940-4393-97e7-3c7b13ee9ad5", "name": "NUMTRANSITEM", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "valuesToSend": {"values": [{"name": "NUMTRANSITEM", "value": "={{ $('NUMTRANSITEM').item.json.NUMTRANSITEM }}"}, {"name": "NUMSEQ", "value": "={{ $('Loop').item.json.nItem }}"}, {"name": "CODOPER", "value": "={{ $('If').item.json.CODOPER }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop').item.json.nNF }}"}, {"name": "VLOUTRASDESP", "value": "={{ $('Loop').item.json.vOutro }}"}, {"name": "DTMOVLOG", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop').item.json.dhEmi.split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $('Loop').item.json.CST_PIS_COFINS }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "1"}, {"name": "CODCLI", "value": "={{ $('If').item.json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('If').item.json.NUMTRANSVENDA }}"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT"}, {"name": "CODFORNEC", "value": "=(SELECT CODFORNEC FROM PCPRODUT WHERE CODPROD={{ $('Loop').item.json.cProd }})"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA", "value": "={{ $json.CODFILIAL }}"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO", "value": "N"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "CODPROD", "value": "={{ $('Loop').item.json.cProd }}"}, {"name": "CODAUXILIAR", "value": "={{ $('Loop').item.json.cEAN | 0 }}"}, {"name": "NBM", "value": "={{ $('Loop').item.json.ncm }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop').item.json.CFOP }}"}, {"name": "UNIDADE", "value": "={{ $('Loop').item.json.uCom }}"}, {"name": "QT", "value": "={{ $('Loop').item.json.qCom }}"}, {"name": "QTCONT", "value": "={{ $('Loop').item.json.qCom }}"}, {"name": "PUNIT", "value": "={{ $('Loop').item.json.vUnCom }}"}, {"name": "PUNITCONT", "value": "={{ $('Loop').item.json.PUNITCONT }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $('Loop').item.json.cProd }})"}, {"name": "VLDESCONTO", "value": "={{ $('Loop').item.json.vDescItem }}"}, {"name": "PERCICM", "value": "={{ $('Loop').item.json.pICMS | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $('Loop').item.json.CST }}"}, {"name": "VLFRETE", "value": "={{ $('Loop').item.json.vFreteItem }}"}, {"name": "BASEICMS", "value": "={{ $('Loop').item.json.vBC_ICMS }}"}, {"name": "BASEICMSBCR", "value": "0"}, {"name": "BASEBCR", "value": "0"}, {"name": "STBCR", "value": "={{ $('Loop').item.json.STBCR }}"}, {"name": "VLICMSBCR", "value": "={{ $('Loop').item.json.VLICMSBCR }}"}, {"name": "BASEICST", "value": "={{ $('Loop').item.json.BASEICST }}"}, {"name": "ST", "value": "={{ $('Loop').item.json.ST }}"}, {"name": "PERCST", "value": "={{ $('Loop').item.json.PERCST }}"}, {"name": "IVA", "value": "={{ $('Loop').item.json.IVA }}"}, {"name": "PERPIS", "value": "={{ $('Loop').item.json.pPIS }}"}, {"name": "VLPIS", "value": "={{ $('Loop').item.json.vPIS }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $('Loop').item.json.VLBASEPISCOFINS }}"}, {"name": "PERCOFINS", "value": "={{ $('Loop').item.json.pCOFINS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop').item.json.vCOFINS }}"}, {"name": "VLIPI", "value": "={{ $('Loop').item.json.vIPIItem }}"}, {"name": "PERCIPI", "value": "={{ $('Loop').item.json.pIPI }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop').item.json.vBC_IPI }}"}, {"name": "NUMLOTE"}, {"name": "CODICMTAB", "value": "={{ $json.CODICMTAB }}"}, {"name": "CODST", "value": "={{ $json.CODST }}"}, {"name": "PTABELA", "value": "={{ $('Loop').item.json.vUnCom }}"}, {"name": "CUSTOFIN", "value": "={{ $json.CMV }}"}, {"name": "CUSTOREAL", "value": "={{ $json.CMV }}"}, {"name": "CUSTOREP", "value": "={{ $json.CUSTOREP }}"}, {"name": "CUSTOCONT", "value": "={{ $json.CUSTOCONT }}"}, {"name": "CUSTOFINEST", "value": "={{ $json.CUSTOFIN }}"}, {"name": "CODUSUR", "value": "={{ $('Loop').item.json.CODRCA }}"}, {"name": "NUMPED", "value": "={{ $('Loop').item.json.xPed | 0 }}"}, {"name": "NUMREGIAO", "value": "=(SELECT NVL(REGCLI.NUMREGIAO, P.NUMRE<PERSON>) NUMREGIAO\n  FROM PCCLIENT C,\n       PCPRACA P,\n       (SELECT PCTABPRCLI.CODCLI,\n               PCTABPRCLI.NUMREGIAO\n           FROM PCTABPRCLI,\n                <PERSON>REGIAO\n           WHERE PCTABPRCLI.NUMREGIAO = PCREGIAO.NUMREGIAO\n             AND PCTABPRCLI.CODFILIALNF = '{{ $json.CODFILIAL }}') REGCLI\n  WHERE C.CODPRACA = P.CODPRACA\n    AND C.CODCLI = REGCLI.CODCLI (+)\n    AND TO_CHAR(C.CODCLI) = '{{ $('If').item.json.CODCLI }}')"}, {"name": "CODEPTO", "value": "=(SELECT CODEPTO FROM PCPRODUT WHERE CODPROD={{ $('Loop').item.json.cProd }})"}, {"name": "CODSEC", "value": "=(SELECT CODSEC FROM PCPRODUT WHERE CODPROD={{ $('Loop').item.json.cProd }})"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [980, 560], "id": "4136acd3-57bd-45d4-a155-65d60bd1fbc9", "name": "PCMOV", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "list", "cachedResultName": "PCMOVCOMPLE"}, "valuesToSend": {"values": [{"name": "NUMTRANSITEM", "value": "={{ $('NUMTRANSITEM').item.json.NUMTRANSITEM }}"}, {"name": "DTREGISTRO", "value": "={{ $('Loop').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop').item.json.dhEmi.split('T')[1].split('-')[0] }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('Loop').item.json.pST }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('Loop').item.json.vBCUFDest }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('Loop').item.json.pICMSUFDest }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('Loop').item.json.pICMSInter }}"}, {"name": "PERCPROVPART", "value": "={{ $('Loop').item.json.pICMSInterPart }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('Loop').item.json.VLICMSPARTDEST }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('Loop').item.json.VLICMSPARTREM }}"}, {"name": "ALIQFCP", "value": "={{ $('Loop').item.json.pFCPUFDest }}"}, {"name": "VLFCPPART", "value": "={{ $('Loop').item.json.VLFCPPART }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('Loop').item.json.chNFe }}"}, {"name": "CODPRODGNRE", "value": "={{ $('Loop').item.json.cProd }}"}, {"name": "EANCODPROD", "value": "={{ $('Loop').item.json.cEAN | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('Loop').item.json.nItem }}"}, {"name": "CODCEST", "value": "={{ $('Loop').item.json.CODCEST }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "={{ $('Loop').item.json.vFreteItem }}"}, {"name": "VLBASEOUTROS", "value": "={{ $('Loop').item.json.vOutro }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1160, 560], "id": "492b03c2-258c-4c41-8a81-cbb3ef77e9ae", "name": "PCMOVCOMPLE", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSVENDA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $json.chNFe }}'\n   AND EXISTS (SELECT NUMTRANSVENDA\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = N.NUMTRANSVENDA\n          AND TRUNC(DTMOV) < TRUNC(DTMOVLOG)\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [440, 780], "id": "c1ea505f-530b-455a-944a-fbcc7ba729cc", "name": "Valida PCMOV", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5005be8c-c598-4d07-a6f2-7691b277d654", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [620, 780], "id": "243b4d01-2a5d-4ac6-aaeb-f7992a7d2360", "name": "If1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.CUSTOFIN,\n           E.CUSTOREAL,\n           E.CUSTOREP,\n           E.CUSTOCONT,\n           ((({{ $('Loop').item.json.vUnCom }} * C.CODICMTAB) / 100) + (({{ $('Loop').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $('If').item.json.CODFILIAL }}'\n        AND T.UFDESTINO = '{{ $('Loop').item.json.ufDest }}'\n        AND E.CODPROD = {{ $('Loop').item.json.cProd }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $('Loop').item.json.vUnCom }} * T.CODICMTAB) / 100) + (({{ $('Loop').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $('Loop').item.json.ufDest }}'\n    AND E.CODPROD = {{ $('Loop').item.json.cProd }}\n    AND E.CODFILIAL = '{{ $('If').item.json.CODFILIAL }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [800, 560], "id": "6ff1b0cd-6f3a-4342-a250-2e5ccad34c74", "name": "CUSTOS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT ROUND(SUM(CUSTOFIN * QT), 2) AS VLCUSTOFIN,\n       ROUND(SUM(CUSTOREAL * QT), 2) AS VLCUSTOREAL,\n       ROUND(SUM(CUSTOREP * QT), 2) AS VLCUSTOREP,\n       ROUND(SUM(CUSTOCONT * QT), 2) AS VLCUSTOCONT\n  FROM PCMOV\n WHERE PCMOV.NUMTRANSVENDA = {{ $json[\"NUMTRANSVENDA\"] }}", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [800, 800], "id": "672ea38f-3710-406d-8c43-e6e7365eebc8", "name": "VLCUSTOS", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCNFSAID", "mode": "list", "cachedResultName": "PCNFSAID"}, "where": {"values": [{"column": "NUMTRANSVENDA", "value": "={{ $('If1').item.json.NUMTRANSVENDA }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [980, 800], "id": "dccc10ef-7936-46d7-9a41-bbb0b64118dd", "name": "CUSTOS PCNFSAID", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [240, 760], "id": "1df4935b-b64e-4609-a321-6b7b9b565e93", "name": "Loop1"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "where": {"values": [{"column": "NUMTRANSVENDA", "value": "={{ $('If1').item.json.NUMTRANSVENDA }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1160, 800], "id": "a571c326-eaa3-44f1-ac0f-08b9eedcebb4", "name": "Corrigir DTMOVLOG", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT S.CODCLI,\n       S.<PERSON>SVENDA,\n       S.CODFILIAL,\n       CASE WHEN TIPOVENDA = '1' THEN 'S' \n            WHEN TIPOVENDA = 'SR' THEN 'SR' \n        END CODOPER\n  FROM PCNFSAID S\n  WHERE S.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n    AND S.DTCANCEL IS NULL\n    AND NOT EXISTS (SELECT NUMTRANSVENDA FROM PCMOV WHERE NUMTRANSVENDA = S.NUMTRANSVENDA AND ROWNUM = 1)\n    AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [240, 1160], "id": "a911b6a0-5a49-4563-be43-38425e5c8f52", "name": "NUMTRANSVENDA1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ee4ae02c-b9c4-41ae-9a71-6a1e95656634", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [420, 1160], "id": "d059fffb-4404-43e5-b5bd-afd87182f982", "name": "If2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [40, 1140], "id": "606a7bb5-3fee-4f1d-b991-929344ed8e3f", "name": "Loop2"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [620, 1180], "id": "c8d768eb-5786-490f-a53f-fb04a4bdf12d", "name": "NUMTRANSITEM1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "valuesToSend": {"values": [{"name": "NUMTRANSITEM", "value": "={{ $('NUMTRANSITEM1').item.json.NUMTRANSITEM }}"}, {"name": "NUMSEQ", "value": "={{ $('Loop2').item.json.nItem }}"}, {"name": "CODOPER", "value": "={{ $('If2').item.json.CODOPER }}"}, {"name": "NUMNOTA", "value": "={{ $('Loop2').item.json.nNF }}"}, {"name": "VLOUTRASDESP", "value": "={{ $('Loop2').item.json.vOutro }}"}, {"name": "DTMOVLOG", "value": "={{ $('Loop2').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop2').item.json.dhEmi.split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $('Loop2').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $('Loop2').item.json.CST_PIS_COFINS }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "1"}, {"name": "CODCLI", "value": "={{ $('If2').item.json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('If2').item.json.NUMTRANSVENDA }}"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT"}, {"name": "CODFORNEC", "value": "=(SELECT CODFORNEC FROM PCPRODUT WHERE CODPROD={{ $('Loop2').item.json.cProd }})"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "CODPROD", "value": "={{ $('Loop2').item.json.cProd }}"}, {"name": "CODAUXILIAR", "value": "={{ $('Loop2').item.json.cEAN | 0 }}"}, {"name": "NBM", "value": "={{ $('Loop2').item.json.ncm }}"}, {"name": "CODFISCAL", "value": "={{ $('Loop2').item.json.CFOP }}"}, {"name": "UNIDADE", "value": "={{ $('Loop2').item.json.uCom }}"}, {"name": "QT", "value": "={{ $('Loop2').item.json.qCom }}"}, {"name": "QTCONT", "value": "={{ $('Loop2').item.json.qCom }}"}, {"name": "PUNIT", "value": "={{ $('Loop2').item.json.vUnCom }}"}, {"name": "PUNITCONT", "value": "={{ $('Loop2').item.json.PUNITCONT }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $('Loop2').item.json.cProd }})"}, {"name": "VLDESCONTO", "value": "={{ $('Loop2').item.json.vDescItem }}"}, {"name": "PERCICM", "value": "={{ $('Loop2').item.json.pICMS | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $('Loop2').item.json.CST }}"}, {"name": "VLFRETE", "value": "={{ $('Loop2').item.json.vFreteItem }}"}, {"name": "BASEICMS", "value": "={{ $('Loop2').item.json.vBC_ICMS }}"}, {"name": "BASEICMSBCR", "value": "0"}, {"name": "BASEBCR", "value": "0"}, {"name": "STBCR", "value": "={{ $('Loop2').item.json.STBCR }}"}, {"name": "VLICMSBCR", "value": "={{ $('Loop2').item.json.VLICMSBCR }}"}, {"name": "BASEICST", "value": "={{ $('Loop2').item.json.BASEICST }}"}, {"name": "ST", "value": "={{ $('Loop2').item.json.ST }}"}, {"name": "PERCST", "value": "={{ $('Loop2').item.json.PERCST }}"}, {"name": "IVA", "value": "={{ $('Loop2').item.json.IVA }}"}, {"name": "PERPIS", "value": "={{ $('Loop2').item.json.pPIS }}"}, {"name": "VLPIS", "value": "={{ $('Loop2').item.json.vPIS }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $('Loop2').item.json.VLBASEPISCOFINS }}"}, {"name": "PERCOFINS", "value": "={{ $('Loop2').item.json.pCOFINS }}"}, {"name": "VLCOFINS", "value": "={{ $('Loop2').item.json.vCOFINS }}"}, {"name": "VLIPI", "value": "={{ $('Loop2').item.json.vIPIItem }}"}, {"name": "PERCIPI", "value": "={{ $('Loop2').item.json.pIPI }}"}, {"name": "VLBASEIPI", "value": "={{ $('Loop2').item.json.vBC_IPI }}"}, {"name": "NUMLOTE"}, {"name": "CODICMTAB", "value": "={{ $json.CODICMTAB }}"}, {"name": "CODST", "value": "={{ $json.CODST }}"}, {"name": "PTABELA", "value": "={{ $('Loop2').item.json.vUnCom }}"}, {"name": "CUSTOFIN", "value": "={{ $json.CMV }}"}, {"name": "CUSTOREAL", "value": "={{ $json.CMV }}"}, {"name": "CUSTOREP", "value": "={{ $json.CUSTOREP }}"}, {"name": "CUSTOCONT", "value": "={{ $json.CUSTOCONT }}"}, {"name": "CUSTOFINEST", "value": "={{ $json.CUSTOFIN }}"}, {"name": "CODUSUR", "value": "={{ $('Loop2').item.json.CODRCA }}"}, {"name": "NUMPED", "value": "={{ $('Loop2').item.json.xPed | 0 }}"}, {"name": "NUMREGIAO", "value": "=(SELECT NVL(REGCLI.NUMREGIAO, P.NUMRE<PERSON>) NUMREGIAO\n  FROM PCCLIENT C,\n       PCPRACA P,\n       (SELECT PCTABPRCLI.CODCLI,\n               PCTABPRCLI.NUMREGIAO\n           FROM PCTABPRCLI,\n                <PERSON>REGIAO\n           WHERE PCTABPRCLI.NUMREGIAO = PCREGIAO.NUMREGIAO\n             AND PCTABPRCLI.CODFILIALNF = '{{ $json.CODFILIAL }}') REGCLI\n  WHERE C.CODPRACA = P.CODPRACA\n    AND C.CODCLI = REGCLI.CODCLI (+)\n    AND TO_CHAR(C.CODCLI) = '{{ $('If2').item.json.CODCLI }}')"}, {"name": "CODEPTO", "value": "=(SELECT CODEPTO FROM PCPRODUT WHERE CODPROD={{ $('Loop2').item.json.cProd }})"}, {"name": "CODSEC", "value": "=(SELECT CODSEC FROM PCPRODUT WHERE CODPROD={{ $('Loop2').item.json.cProd }})"}, {"name": "PERCBASERED", "value": "100"}, {"name": "PERCBASEREDST", "value": "100"}, {"name": "NUMSEQPED", "value": "={{ $('Loop2').item.json.nItem }}"}, {"name": "APROVEITACREDPISCOFINS", "value": "S"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [980, 1180], "id": "52c9876a-3abf-4f48-ba1f-0a4c2ac88334", "name": "PCMOV1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "list", "cachedResultName": "PCMOVCOMPLE"}, "valuesToSend": {"values": [{"name": "NUMTRANSITEM", "value": "={{ $('NUMTRANSITEM1').item.json.NUMTRANSITEM }}"}, {"name": "DTREGISTRO", "value": "={{ $('Loop2').item.json.dhEmi.split('T')[0].split('-').reverse().join('/') + \" \" + $('Loop2').item.json.dhEmi.split('T')[1].split('-')[0] }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('Loop2').item.json.pST }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('Loop2').item.json.vBCUFDest }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('Loop2').item.json.pICMSUFDest }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('Loop2').item.json.pICMSInter }}"}, {"name": "PERCPROVPART", "value": "={{ $('Loop2').item.json.pICMSInterPart }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('Loop2').item.json.VLICMSPARTDEST }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('Loop2').item.json.VLICMSPARTREM }}"}, {"name": "ALIQFCP", "value": "={{ $('Loop2').item.json.pFCPUFDest }}"}, {"name": "VLFCPPART", "value": "={{ $('Loop2').item.json.VLFCPPART }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('Loop2').item.json.chNFe }}"}, {"name": "CODPRODGNRE", "value": "={{ $('Loop2').item.json.cProd }}"}, {"name": "EANCODPROD", "value": "={{ $('Loop2').item.json.cEAN | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('Loop2').item.json.nItem }}"}, {"name": "CODCEST", "value": "={{ $('Loop2').item.json.CODCEST }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "={{ $('Loop2').item.json.vFreteItem }}"}, {"name": "VLBASEOUTROS", "value": "={{ $('Loop2').item.json.vOutro }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1160, 1180], "id": "496f3f3c-2eb7-4844-8615-bb1939191ae3", "name": "PCMOVCOMPLE1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSVENDA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $json.chNFe }}'\n   AND EXISTS (SELECT NUMTRANSVENDA\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = N.NUMTRANSVENDA\n          AND TRUNC(DTMOV) < TRUNC(DTMOVLOG)\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [440, 1400], "id": "1354e98d-d96a-4fe7-b420-ff7c9d9919fa", "name": "Valida PCMOV1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5005be8c-c598-4d07-a6f2-7691b277d654", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [620, 1400], "id": "9a8d6708-e64d-43da-8298-9159e2c55b91", "name": "If3"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.C<PERSON>TOFIN,\n           E.CUSTOREAL,\n           E.<PERSON>TOREP,\n           E.CUSTOCONT,\n           ((({{ $('Loop2').item.json.vUnCom }} * C.CODICMTAB) / 100) + (({{ $('Loop2').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $('If2').item.json.CODFILIAL }}'\n        AND T.UFDESTINO = '{{ $('Loop2').item.json.ufDest }}'\n        AND E.CODPROD = {{ $('Loop2').item.json.cProd }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $('Loop2').item.json.vUnCom }} * T.CODICMTAB) / 100) + (({{ $('Loop2').item.json.vUnCom }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $('Loop2').item.json.ufDest }}'\n    AND E.CODPROD = {{ $('Loop2').item.json.cProd }}\n    AND E.CODFILIAL = '{{ $('If2').item.json.CODFILIAL }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [800, 1180], "id": "9874dd8d-e28e-4c6b-9c62-77a84e721303", "name": "CUSTOS1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT ROUND(SUM(CUSTOFIN * QT), 2) AS VLCUSTOFIN,\n       ROUND(SUM(CUSTOREAL * QT), 2) AS VLCUSTOREAL,\n       ROUND(SUM(CUSTOREP * QT), 2) AS VLCUSTOREP,\n       ROUND(SUM(CUSTOCONT * QT), 2) AS VLCUSTOCONT\n  FROM PCMOV\n WHERE PCMOV.NUMTRANSVENDA = {{ $json[\"NUMTRANSVENDA\"] }}", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [800, 1420], "id": "42de1029-22cf-4d96-aa24-d1418d3a0e2b", "name": "VLCUSTOS1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCNFSAID", "mode": "list", "cachedResultName": "PCNFSAID"}, "where": {"values": [{"column": "NUMTRANSVENDA", "value": "={{ $('If3').item.json.NUMTRANSVENDA }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [980, 1420], "id": "730e39a2-a275-42f1-93b8-312ee0988d57", "name": "CUSTOS PCNFSAID1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [240, 1380], "id": "ef835748-c8ae-45fe-a749-359205e55504", "name": "Loop3"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCMOV", "mode": "list", "cachedResultName": "PCMOV"}, "where": {"values": [{"column": "NUMTRANSVENDA", "value": "={{ $('If3').item.json.NUMTRANSVENDA }}"}]}, "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [1160, 1420], "id": "fcff30d2-7318-461f-a0d8-d2322da638c4", "name": "Corrigir DTMOVLOG1", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "BASE TESTE"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [40, 760], "id": "b1daba7a-e8bb-4c02-8069-dd4e11133844", "name": "Remove Duplicates"}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [40, 1380], "id": "ecb93d2f-818d-4b7f-a927-4500c6f6a202", "name": "Remove Duplicates1"}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-1240, 960], "id": "94444e4d-b168-4624-95c3-054ef3b3155b", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-1080, 960], "id": "352b146b-e3c8-4197-8d7e-426b4e40938b", "name": "Extract from File"}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "Loop", "type": "main", "index": 0}, {"node": "Remove Duplicates", "type": "main", "index": 0}], [{"node": "Loop2", "type": "main", "index": 0}, {"node": "Remove Duplicates1", "type": "main", "index": 0}]]}, "Saida": {"main": [[{"node": "natOp", "type": "main", "index": 0}], []]}, "NUMTRANSVENDA": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "NUMTRANSITEM", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "NUMTRANSVENDA", "type": "main", "index": 0}]]}, "NUMTRANSITEM": {"main": [[{"node": "CUSTOS", "type": "main", "index": 0}]]}, "PCMOV": {"main": [[{"node": "PCMOVCOMPLE", "type": "main", "index": 0}]]}, "PCMOVCOMPLE": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Valida PCMOV": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}], [{"node": "VLCUSTOS", "type": "main", "index": 0}]]}, "CUSTOS": {"main": [[{"node": "PCMOV", "type": "main", "index": 0}]]}, "VLCUSTOS": {"main": [[{"node": "CUSTOS PCNFSAID", "type": "main", "index": 0}]]}, "CUSTOS PCNFSAID": {"main": [[{"node": "Corrigir DTMOVLOG", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Valida PCMOV", "type": "main", "index": 0}]]}, "Corrigir DTMOVLOG": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "NUMTRANSVENDA1": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Loop2", "type": "main", "index": 0}], [{"node": "NUMTRANSITEM1", "type": "main", "index": 0}]]}, "Loop2": {"main": [[], [{"node": "NUMTRANSVENDA1", "type": "main", "index": 0}]]}, "NUMTRANSITEM1": {"main": [[{"node": "CUSTOS1", "type": "main", "index": 0}]]}, "PCMOV1": {"main": [[{"node": "PCMOVCOMPLE1", "type": "main", "index": 0}]]}, "PCMOVCOMPLE1": {"main": [[{"node": "Loop2", "type": "main", "index": 0}]]}, "Valida PCMOV1": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Loop3", "type": "main", "index": 0}], [{"node": "VLCUSTOS1", "type": "main", "index": 0}]]}, "CUSTOS1": {"main": [[{"node": "PCMOV1", "type": "main", "index": 0}]]}, "VLCUSTOS1": {"main": [[{"node": "CUSTOS PCNFSAID1", "type": "main", "index": 0}]]}, "CUSTOS PCNFSAID1": {"main": [[{"node": "Corrigir DTMOVLOG1", "type": "main", "index": 0}]]}, "Loop3": {"main": [[], [{"node": "Valida PCMOV1", "type": "main", "index": 0}]]}, "Corrigir DTMOVLOG1": {"main": [[{"node": "Loop3", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "Remove Duplicates1": {"main": [[{"node": "Loop3", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}