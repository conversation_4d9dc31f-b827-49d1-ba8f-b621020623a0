// 3-Not<PERSON> de Entrada/Saida
let results = []

for (let itemIndex = 0; itemIndex < items.length; itemIndex++) {
  const item = items[itemIndex]
  const xmlData = item.json // Usar a estrutura de dados do item atual

  if (
    xmlData &&
    xmlData['nfeProc'] &&
    xmlData['nfeProc']['NFe'] &&
    xmlData['nfeProc']['NFe']['infNFe'] &&
    xmlData['nfeProc']['NFe']['infNFe']['det']
  ) {
    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']

    // Certificar-se de que detList seja sempre um array
    if (!Array.isArray(detList)) {
      detList = [detList]
    }
    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']
    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)
    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)
    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)
    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) 

    for (const det of detList) {
      if (det['prod']) {
        const produto = det['prod']
        const imposto = det['imposto']
        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);
        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);
        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']
        const icms = imposto && imposto['ICMS']
        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']
        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal
        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][
          'verProc'
        ].includes('mercadolivre')
          ? 'CML'
          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(
              'Bling'
            )
          ? 'D2C'
          : ''
        // BC de IPI = V. TOTAL PRODUTOS
        const VLBASEIPI = parseFloat(vProdTotal)

        // Encontrar o primeiro objeto ICMS que existe no XML
        const icmsObj = [
          'ICMS00',
          'ICMS10',
          'ICMS20',
          'ICMS30',
          'ICMS40',
          'ICMS50',
          'ICMS60',
          'ICMS70',
          'ICMS80',
          'ICMSSN101',
          'ICMSSN102',
          'ICMSSN103',
          'ICMSSN201',
          'ICMSSN202',
          'ICMSSN203',
          'ICMSSN300',
          'ICMSSN400',
          'ICMSSN500',
          'ICMSSN900'
        ].find(obj => icms && icms[obj])
        // Obter o valor da tag CST para PIS ou COFINS
        const CST_PIS_COFINS = pis 
        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') 
        : cofins 
        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') 
        : '00';
        // Calcular os valores conforme as fórmulas fornecidas
        const resultItem = {
          CGCENT: dest['CNPJ'] || dest['CPF'] || null,
          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',
          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',
          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',
          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],
          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],
          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],
          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],
          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],
          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],
          refNFe: [],
          vFrete: vFrete,
          vOutro: vOutro, 
          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],
          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],
          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],
          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,
          vICMS:
            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],
          vProd:
            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],
          BASEICST: parseFloat(
            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']
          ),
          VLBASEIPI: VLBASEIPI,
					/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */
          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, 
          vIPI:
            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||
            0,
          vIPIDevol:
            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][
              'vIPIDevol'
            ] || 0,
          pIPI: ipi ? ipi['pIPI'] || 0 : 0,
          vPIS: pis ? pis['vPIS'] || 0 : 0,
          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,
          cPais: dest['enderDest']['cPais'],
          xPais: dest['enderDest']['xPais'],
          CEP: dest['enderDest']['CEP'],
          ufDest: dest['enderDest']['UF'],
          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],
          xMun: dest['enderDest']['xMun'],
          xLgr: dest['enderDest']['xLgr'],
          nro: dest['enderDest']['nro'],
          xBairro: dest['enderDest']['xBairro'],
          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],
          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],
          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],
          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],
          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],
          CFOP: produto['CFOP'],
          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],
          CODCOB,
          CST_PIS_COFINS, // Variável única para CST
        }
        // Adicione refNFe se existir
        if (
          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&
          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
        ) {
          const refNFeList = Array.isArray(
            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
          )
            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']
            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]

          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')
        }
        // Adicione o resultado ao pairedItem
        if (!item.pairedItem) {
          item.pairedItem = {}
        }
        item.pairedItem = resultItem
        // Adicione o resultado ao array 'results'
        results.push(resultItem)
      }
    }
  }
}

return results
